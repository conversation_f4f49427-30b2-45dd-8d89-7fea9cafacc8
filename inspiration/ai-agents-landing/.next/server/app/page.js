/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9cafe29e63e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9BcHBsaWNhdGlvbnMvQUkgUHJvamVjdCAvcmVhbGZyb250ZW5kL2luc3BpcmF0aW9uL2FpLWFnZW50cy1sYW5kaW5nL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjljYWZlMjllNjNlOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'AI Agents for No-Code Business',\n    description: 'Master the art of building AI-powered businesses without writing a single line of code.',\n    viewport: {\n        width: 'device-width',\n        initialScale: 1,\n        maximumScale: 1,\n        userScalable: false,\n        viewportFit: 'cover'\n    },\n    themeColor: '#000000',\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: 'black-translucent',\n        title: 'AI Agents'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: children\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRkFJJTIwUHJvamVjdCUyMCUyRnJlYWxmcm9udGVuZCUyRmluc3BpcmF0aW9uJTJGYWktYWdlbnRzLWxhbmRpbmclMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL0FJIFByb2plY3QgL3JlYWxmcm9udGVuZC9pbnNwaXJhdGlvbi9haS1hZ2VudHMtbGFuZGluZy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCapability, setCurrentCapability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeFlow, setActiveFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [terminalText, setTerminalText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withoutCodeText, setWithoutCodeText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const terminalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const scale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0.8\n    ]);\n    // Detect mobile device\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const checkMobile = {\n                \"Home.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"Home.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // Animate \"Without Code\" text\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const text = 'Without Code';\n            let currentIndex = 0;\n            let typeInterval;\n            const startTyping = setTimeout({\n                \"Home.useEffect.startTyping\": ()=>{\n                    typeInterval = setInterval({\n                        \"Home.useEffect.startTyping\": ()=>{\n                            if (currentIndex <= text.length) {\n                                setWithoutCodeText(text.slice(0, currentIndex));\n                                currentIndex++;\n                            } else {\n                                clearInterval(typeInterval);\n                            }\n                        }\n                    }[\"Home.useEffect.startTyping\"], 100);\n                }\n            }[\"Home.useEffect.startTyping\"], 500) // Start typing after 500ms\n            ;\n            return ({\n                \"Home.useEffect\": ()=>{\n                    clearTimeout(startTyping);\n                    if (typeInterval) clearInterval(typeInterval);\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    const capabilities = [\n        {\n            title: \"Customer Support\",\n            flow: [\n                \"User Request\",\n                \"AI Analysis\",\n                \"Auto Response\",\n                \"Happy Customer\"\n            ],\n            metric: \"95% Faster Response\"\n        },\n        {\n            title: \"Sales Automation\",\n            flow: [\n                \"Lead Capture\",\n                \"AI Qualification\",\n                \"Personalized Outreach\",\n                \"Closed Deal\"\n            ],\n            metric: \"3x More Conversions\"\n        },\n        {\n            title: \"Content Creation\",\n            flow: [\n                \"Content Brief\",\n                \"AI Generation\",\n                \"Human Polish\",\n                \"Published Asset\"\n            ],\n            metric: \"10x Content Output\"\n        },\n        {\n            title: \"Data Analysis\",\n            flow: [\n                \"Raw Data\",\n                \"AI Processing\",\n                \"Insights Generated\",\n                \"Decision Made\"\n            ],\n            metric: \"Real-time Insights\"\n        }\n    ];\n    const agentExamples = [\n        {\n            name: \"Customer Support\",\n            input: \"I can't login to my account\",\n            process: [\n                \"> Agent: Analyzing user issue...\",\n                \"> Agent: Checking account status...\",\n                \"> Agent: Found: Password reset needed\",\n                \"> Agent: Sending reset instructions...\"\n            ],\n            output: \"Reset link <NAME_EMAIL>. Issue resolved in 12 seconds.\"\n        },\n        {\n            name: \"Data Analysis\",\n            input: \"Analyze Q4 sales performance\",\n            process: [\n                \"> Agent: Loading sales data...\",\n                \"> Agent: Processing 15,423 transactions...\",\n                \"> Agent: Identifying trends...\",\n                \"> Agent: Generating insights...\"\n            ],\n            output: \"Q4 sales up 23% YoY. Top product: AI Suite. Key region: North America.\"\n        },\n        {\n            name: \"Content Creation\",\n            input: \"Write a blog post about AI trends\",\n            process: [\n                \"> Agent: Researching latest AI developments...\",\n                \"> Agent: Analyzing top trends...\",\n                \"> Agent: Generating content structure...\",\n                \"> Agent: Writing article...\"\n            ],\n            output: \"1,500-word blog post created: '5 AI Trends Reshaping Business in 2025'\"\n        }\n    ];\n    // Rotate through capabilities\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setCurrentCapability({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % capabilities.length\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        capabilities.length\n    ]);\n    // Animate flow\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (activeFlow >= 0 && activeFlow < 3) {\n                const timeout = setTimeout({\n                    \"Home.useEffect.timeout\": ()=>{\n                        setActiveFlow({\n                            \"Home.useEffect.timeout\": (prev)=>prev + 1\n                        }[\"Home.useEffect.timeout\"]);\n                    }\n                }[\"Home.useEffect.timeout\"], 1000) // Time between each step\n                ;\n                return ({\n                    \"Home.useEffect\": ()=>clearTimeout(timeout)\n                })[\"Home.useEffect\"];\n            }\n        }\n    }[\"Home.useEffect\"], [\n        activeFlow,\n        currentCapability\n    ]);\n    // Reset activeFlow when capability changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setActiveFlow(-1) // Start before first node\n            ;\n            // Small delay then start animation\n            const startTimeout = setTimeout({\n                \"Home.useEffect.startTimeout\": ()=>{\n                    setActiveFlow(0);\n                }\n            }[\"Home.useEffect.startTimeout\"], 200);\n            return ({\n                \"Home.useEffect\": ()=>clearTimeout(startTimeout)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        currentCapability\n    ]);\n    // Typewriter effect\n    const typewriterEffect = async (text, startIndex = 0)=>{\n        setIsTyping(true);\n        let currentText = text.substring(0, startIndex);\n        for(let i = startIndex; i < text.length; i++){\n            currentText = text.substring(0, i + 1);\n            setTerminalText(currentText);\n            await new Promise((resolve)=>setTimeout(resolve, 30));\n        }\n        setIsTyping(false);\n        return currentText;\n    };\n    // Auto-cycle through terminal examples\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cycleInterval = setInterval({\n                \"Home.useEffect.cycleInterval\": ()=>{\n                    setSelectedAgent({\n                        \"Home.useEffect.cycleInterval\": (prev)=>(prev + 1) % agentExamples.length\n                    }[\"Home.useEffect.cycleInterval\"]);\n                }\n            }[\"Home.useEffect.cycleInterval\"], 8000) // Change every 8 seconds\n            ;\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(cycleInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        agentExamples.length\n    ]);\n    // Animate terminal when agent changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            let isCancelled = false;\n            const animateTerminal = {\n                \"Home.useEffect.animateTerminal\": async ()=>{\n                    const example = agentExamples[selectedAgent];\n                    // Clear terminal\n                    setTerminalText('');\n                    if (isCancelled) return;\n                    await new Promise({\n                        \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"Home.useEffect.animateTerminal\"]);\n                    // Build the full terminal text\n                    let fullText = `> User: \"${example.input}\"\\n`;\n                    // Type input\n                    if (!isCancelled) {\n                        await typewriterEffect(fullText);\n                        await new Promise({\n                            \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 500)\n                        }[\"Home.useEffect.animateTerminal\"]);\n                    }\n                    // Add and type process lines\n                    for (const line of example.process){\n                        if (isCancelled) return;\n                        fullText += line + '\\n';\n                        await typewriterEffect(fullText, fullText.length - line.length - 1);\n                        await new Promise({\n                            \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 300)\n                        }[\"Home.useEffect.animateTerminal\"]);\n                    }\n                    if (isCancelled) return;\n                    await new Promise({\n                        \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 500)\n                    }[\"Home.useEffect.animateTerminal\"]);\n                    // Add and type output\n                    fullText += `> Result: ${example.output}`;\n                    if (!isCancelled) {\n                        await typewriterEffect(fullText, fullText.length - example.output.length - 10);\n                    }\n                }\n            }[\"Home.useEffect.animateTerminal\"];\n            animateTerminal();\n            return ({\n                \"Home.useEffect\": ()=>{\n                    isCancelled = true;\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        selectedAgent\n    ]);\n    const validateEmail = (email)=>{\n        const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return re.test(email);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (!validateEmail(email)) {\n            setError('Please enter a valid email address');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/subscribe', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Something went wrong');\n            }\n            setSuccess(true);\n            setEmail('');\n            // Redirect to build interface after 2 seconds\n            setTimeout(()=>{\n                window.location.href = '/build';\n            }, 2000);\n        } catch (err) {\n            setError('Something went wrong. Please try again.');\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-black text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-black via-black to-gray-950\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute -top-1/2 -left-1/2 w-[200%] h-[200%] opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:100px_100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl font-bold\",\n                                children: \"AI Agents\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/build\",\n                                className: \"px-4 py-2 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors text-sm\",\n                                children: \"Start Building\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                ref: containerRef,\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    style: {\n                        opacity,\n                        scale\n                    },\n                    className: \"w-full max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 1\n                            },\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tighter mb-6\",\n                                    children: [\n                                        \"Build AI Agents\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-3xl sm:text-4xl md:text-5xl lg:text-6xl mt-4 h-[1.2em]\",\n                                            children: [\n                                                withoutCodeText.length > 8 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/70\",\n                                                            children: withoutCodeText.slice(0, 8)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: withoutCodeText.slice(8)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: withoutCodeText\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                withoutCodeText.length < 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                    animate: {\n                                                        opacity: [\n                                                            1,\n                                                            0\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 0.8,\n                                                        repeat: Infinity,\n                                                        repeatType: \"reverse\"\n                                                    },\n                                                    className: \"inline-block w-[3px] h-[0.9em] bg-white/60 ml-1 align-text-bottom\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl md:text-2xl text-white/50 max-w-3xl mx-auto\",\n                                    children: \"Transform your business with intelligent automation. No developers needed. Just drag, drop, and deploy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-[360px] sm:h-[500px] flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"absolute inset-0\",\n                                    children: [\n                                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-x-0 top-[20%] bottom-[20%] left-1/2 -translate-x-1/2 w-0.5 pointer-events-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                                                    animate: {\n                                                        y: [\n                                                            '-100%',\n                                                            '200%'\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"linear\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8 sm:mb-16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-white/80 sm:text-white/90\",\n                                                children: capabilities[currentCapability].title\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex flex-col sm:flex-row items-center justify-between max-w-5xl mx-auto px-4 sm:px-8 gap-2 sm:gap-0\",\n                                            children: capabilities[currentCapability].flow.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            initial: {\n                                                                scale: 0,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: activeFlow >= index ? 1 : 0,\n                                                                opacity: activeFlow >= index ? 1 : 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5,\n                                                                type: \"spring\",\n                                                                stiffness: 200\n                                                            },\n                                                            className: \"relative\",\n                                                            children: [\n                                                                activeFlow === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: index === 0 ? 0 : 0.6\n                                                                    },\n                                                                    className: \"absolute inset-0 rounded-xl overflow-hidden\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                            animate: {\n                                                                                opacity: [\n                                                                                    0.3,\n                                                                                    0.6,\n                                                                                    0.3\n                                                                                ],\n                                                                                scale: [\n                                                                                    1,\n                                                                                    1.05,\n                                                                                    1\n                                                                                ]\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 2,\n                                                                                repeat: Infinity,\n                                                                                ease: \"easeInOut\"\n                                                                            },\n                                                                            className: \"absolute inset-0 rounded-xl border border-white/40\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                            animate: {\n                                                                                rotate: [\n                                                                                    0,\n                                                                                    360\n                                                                                ]\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 4,\n                                                                                repeat: Infinity,\n                                                                                ease: \"linear\"\n                                                                            },\n                                                                            className: \"absolute inset-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                                style: {\n                                                                                    background: 'conic-gradient(from 0deg, transparent 70%, rgba(255,255,255,0.3), transparent 80%)',\n                                                                                    maskImage: 'radial-gradient(ellipse at center, transparent 30%, black 70%)'\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `relative bg-black backdrop-blur-sm border rounded-xl px-4 sm:px-6 py-2.5 sm:py-4 min-w-[120px] sm:min-w-[150px] text-center transition-all duration-300 ${activeFlow === index ? 'border-white/40' : 'border-white/20'}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs sm:text-sm font-medium text-white/80 relative z-10\",\n                                                                        children: step\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        index < capabilities[currentCapability].flow.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            animate: {\n                                                                scale: activeFlow > index ? 1 : 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: 0.2,\n                                                                ease: \"easeOut\"\n                                                            },\n                                                            className: \"relative h-6 w-0.5 sm:h-px sm:w-auto sm:flex-1 mx-2\",\n                                                            style: {\n                                                                transformOrigin: isMobile ? 'top center' : 'left center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-b sm:bg-gradient-to-r from-white/10 to-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                activeFlow === index + 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                        initial: isMobile ? {\n                                                                            y: '-100%'\n                                                                        } : {\n                                                                            x: '-100%'\n                                                                        },\n                                                                        animate: isMobile ? {\n                                                                            y: [\n                                                                                '0%',\n                                                                                '200%'\n                                                                            ]\n                                                                        } : {\n                                                                            x: [\n                                                                                '0%',\n                                                                                '200%'\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.8,\n                                                                            ease: \"easeOut\",\n                                                                            times: [\n                                                                                0,\n                                                                                1\n                                                                            ]\n                                                                        },\n                                                                        className: `absolute ${isMobile ? 'w-full h-12' : 'h-full w-12'}`,\n                                                                        style: {\n                                                                            background: isMobile ? 'linear-gradient(to bottom, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)' : 'linear-gradient(to right, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: activeFlow === 3 ? 1 : 0,\n                                                y: activeFlow === 3 ? 0 : 20\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            className: \"absolute -bottom-12 sm:-bottom-16 left-0 right-0 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl md:text-2xl font-bold text-white/70 sm:text-white/90\",\n                                                children: capabilities[currentCapability].metric\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, currentCapability, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 2\n                            },\n                            className: \"absolute bottom-8 left-1/2 -translate-x-1/2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.svg, {\n                                animate: {\n                                    y: [\n                                        0,\n                                        8,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                },\n                                className: \"w-5 h-5 text-white/20\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-[200px] pointer-events-none overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-1/2 -translate-x-1/2 w-0.5 h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                            animate: {\n                                y: [\n                                    '-100%',\n                                    '200%'\n                                ]\n                            },\n                            transition: {\n                                duration: 3,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 549,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"w-full max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl sm:text-5xl md:text-6xl font-bold mb-6\",\n                                    children: \"How AI Agents Work\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-white/50 max-w-3xl mx-auto\",\n                                    children: \"Watch AI agents process requests in real-time. No code required.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -50\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 px-4 py-3 border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-white/40 ml-auto\",\n                                                            children: \"AI Agent Terminal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: terminalRef,\n                                                    className: \"p-4 sm:p-6 font-mono text-xs sm:text-sm min-h-[300px] sm:min-h-[400px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-white/80 whitespace-pre-wrap\",\n                                                        children: [\n                                                            terminalText,\n                                                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex gap-2 flex-wrap\",\n                                            children: agentExamples.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedAgent(index),\n                                                    className: `px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 ${selectedAgent === index ? 'bg-white/10 text-white/90 border border-white/20' : 'bg-white/5 text-white/40 border border-white/10 hover:bg-white/10'}`,\n                                                    children: agent.name\n                                                }, index, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: isMobile ? 0 : 50,\n                                        y: isMobile ? 50 : 0\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative mt-8 lg:mt-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 -translate-x-1/2 w-0.5 h-full pointer-events-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                                                            animate: {\n                                                                y: [\n                                                                    '-100%',\n                                                                    '200%'\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 3,\n                                                                repeat: Infinity,\n                                                                ease: \"linear\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: `linear-gradient(45deg, transparent, white, transparent)`,\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 0\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"User Input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Natural language request\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: `linear-gradient(45deg, transparent, white, transparent)`,\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 0.5\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white/60\",\n                                                                                fill: \"none\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                stroke: \"currentColor\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 1.5,\n                                                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                    lineNumber: 754,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 753,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                                animate: {\n                                                                                    rotate: 360\n                                                                                },\n                                                                                transition: {\n                                                                                    duration: 3,\n                                                                                    repeat: Infinity,\n                                                                                    ease: \"linear\"\n                                                                                },\n                                                                                className: \"absolute inset-0 rounded-lg\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-white/60 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                    lineNumber: 762,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"AI Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Understanding intent\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: `linear-gradient(45deg, transparent, white, transparent)`,\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 1\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"Actions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 818,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Automated execution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: `linear-gradient(45deg, transparent, white, transparent)`,\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 1.5\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 864,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"Results\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 870,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Value delivered\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-2 sm:gap-4 mt-8 sm:mt-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"100ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Response Time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"∞\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Scale Limit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Code Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"w-full max-w-xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-12 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        scale: 0\n                                    },\n                                    whileInView: {\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-4xl font-bold text-white/90\",\n                                            children: \"24/7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/40\",\n                                            children: \"Agent Building\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-12 bg-white/10\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        scale: 0\n                                    },\n                                    whileInView: {\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-4xl font-bold text-white/90\",\n                                            children: \"100%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/40\",\n                                            children: \"Done For You\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold mb-6\",\n                                    children: \"Type What You Want\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-white/50 mb-12\",\n                                    children: \"Our agents build. You deploy instantly.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: email,\n                                                    onChange: (e)=>{\n                                                        setEmail(e.target.value);\n                                                        setError('');\n                                                    },\n                                                    placeholder: \"Enter your email\",\n                                                    className: \"w-full px-6 py-4 text-lg bg-white/[0.03] border border-white/10 rounded-xl text-white outline-none transition-all duration-300 placeholder:text-white/30 hover:bg-white/[0.05] focus:bg-white/[0.07] focus:border-white/20\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    className: \"absolute -bottom-6 left-0 text-red-500 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: loading || success,\n                                            className: \"w-full px-8 py-4 text-lg font-semibold bg-white text-black rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(255,255,255,0.2)] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10\",\n                                                    children: loading ? 'Joining...' : success ? 'Welcome aboard!' : 'Get Early Access'\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 947,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            whileInView: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"grid grid-cols-3 gap-8 mt-20\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Instant Deploy\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Infinite Scale\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1024,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Zero Code\"\n                                }\n                            ].map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center\",\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        type: \"spring\",\n                                        stiffness: 300\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex p-3 rounded-xl bg-white/[0.03] text-white/40 mb-3 group-hover:text-white/60 transition-colors\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/50\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 998,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 913,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                className: \"fixed inset-0 bg-black/90 backdrop-blur-md flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        type: \"spring\",\n                        duration: 0.5\n                    },\n                    className: \"bg-white text-black px-8 py-6 rounded-2xl font-semibold text-lg\",\n                    children: \"Welcome aboard! Redirecting you to the community...\"\n                }, void 0, false, {\n                    fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 1054,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n                lineNumber: 1049,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/AI Project /realfrontend/inspiration/ai-agents-landing/app/page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRkFJJTIwUHJvamVjdCUyMCUyRnJlYWxmcm9udGVuZCUyRmluc3BpcmF0aW9uJTJGYWktYWdlbnRzLWxhbmRpbmclMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL0FJIFByb2plY3QgL3JlYWxmcm9udGVuZC9pbnNwaXJhdGlvbi9haS1hZ2VudHMtbGFuZGluZy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Frealfrontend%2Finspiration%2Fai-agents-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();