"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCapability, setCurrentCapability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeFlow, setActiveFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [terminalText, setTerminalText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withoutCodeText, setWithoutCodeText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const terminalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const scale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0.8\n    ]);\n    // Detect mobile device\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const checkMobile = {\n                \"Home.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"Home.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // Animate \"Without Code\" text\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const text = 'Without Code';\n            let currentIndex = 0;\n            let typeInterval;\n            const startTyping = setTimeout({\n                \"Home.useEffect.startTyping\": ()=>{\n                    typeInterval = setInterval({\n                        \"Home.useEffect.startTyping\": ()=>{\n                            if (currentIndex <= text.length) {\n                                setWithoutCodeText(text.slice(0, currentIndex));\n                                currentIndex++;\n                            } else {\n                                clearInterval(typeInterval);\n                            }\n                        }\n                    }[\"Home.useEffect.startTyping\"], 100);\n                }\n            }[\"Home.useEffect.startTyping\"], 500) // Start typing after 500ms\n            ;\n            return ({\n                \"Home.useEffect\": ()=>{\n                    clearTimeout(startTyping);\n                    if (typeInterval) clearInterval(typeInterval);\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    const capabilities = [\n        {\n            title: \"Customer Support\",\n            flow: [\n                \"User Request\",\n                \"AI Analysis\",\n                \"Auto Response\",\n                \"Happy Customer\"\n            ],\n            metric: \"95% Faster Response\"\n        },\n        {\n            title: \"Sales Automation\",\n            flow: [\n                \"Lead Capture\",\n                \"AI Qualification\",\n                \"Personalized Outreach\",\n                \"Closed Deal\"\n            ],\n            metric: \"3x More Conversions\"\n        },\n        {\n            title: \"Content Creation\",\n            flow: [\n                \"Content Brief\",\n                \"AI Generation\",\n                \"Human Polish\",\n                \"Published Asset\"\n            ],\n            metric: \"10x Content Output\"\n        },\n        {\n            title: \"Data Analysis\",\n            flow: [\n                \"Raw Data\",\n                \"AI Processing\",\n                \"Insights Generated\",\n                \"Decision Made\"\n            ],\n            metric: \"Real-time Insights\"\n        }\n    ];\n    const agentExamples = [\n        {\n            name: \"Customer Support\",\n            input: \"I can't login to my account\",\n            process: [\n                \"> Agent: Analyzing user issue...\",\n                \"> Agent: Checking account status...\",\n                \"> Agent: Found: Password reset needed\",\n                \"> Agent: Sending reset instructions...\"\n            ],\n            output: \"Reset link <NAME_EMAIL>. Issue resolved in 12 seconds.\"\n        },\n        {\n            name: \"Data Analysis\",\n            input: \"Analyze Q4 sales performance\",\n            process: [\n                \"> Agent: Loading sales data...\",\n                \"> Agent: Processing 15,423 transactions...\",\n                \"> Agent: Identifying trends...\",\n                \"> Agent: Generating insights...\"\n            ],\n            output: \"Q4 sales up 23% YoY. Top product: AI Suite. Key region: North America.\"\n        },\n        {\n            name: \"Content Creation\",\n            input: \"Write a blog post about AI trends\",\n            process: [\n                \"> Agent: Researching latest AI developments...\",\n                \"> Agent: Analyzing top trends...\",\n                \"> Agent: Generating content structure...\",\n                \"> Agent: Writing article...\"\n            ],\n            output: \"1,500-word blog post created: '5 AI Trends Reshaping Business in 2025'\"\n        }\n    ];\n    // Rotate through capabilities\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setCurrentCapability({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % capabilities.length\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        capabilities.length\n    ]);\n    // Animate flow\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (activeFlow >= 0 && activeFlow < 3) {\n                const timeout = setTimeout({\n                    \"Home.useEffect.timeout\": ()=>{\n                        setActiveFlow({\n                            \"Home.useEffect.timeout\": (prev)=>prev + 1\n                        }[\"Home.useEffect.timeout\"]);\n                    }\n                }[\"Home.useEffect.timeout\"], 1000) // Time between each step\n                ;\n                return ({\n                    \"Home.useEffect\": ()=>clearTimeout(timeout)\n                })[\"Home.useEffect\"];\n            }\n        }\n    }[\"Home.useEffect\"], [\n        activeFlow,\n        currentCapability\n    ]);\n    // Reset activeFlow when capability changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setActiveFlow(-1) // Start before first node\n            ;\n            // Small delay then start animation\n            const startTimeout = setTimeout({\n                \"Home.useEffect.startTimeout\": ()=>{\n                    setActiveFlow(0);\n                }\n            }[\"Home.useEffect.startTimeout\"], 200);\n            return ({\n                \"Home.useEffect\": ()=>clearTimeout(startTimeout)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        currentCapability\n    ]);\n    // Typewriter effect\n    const typewriterEffect = async function(text) {\n        let startIndex = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        setIsTyping(true);\n        let currentText = text.substring(0, startIndex);\n        for(let i = startIndex; i < text.length; i++){\n            currentText = text.substring(0, i + 1);\n            setTerminalText(currentText);\n            await new Promise((resolve)=>setTimeout(resolve, 30));\n        }\n        setIsTyping(false);\n        return currentText;\n    };\n    // Auto-cycle through terminal examples\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cycleInterval = setInterval({\n                \"Home.useEffect.cycleInterval\": ()=>{\n                    setSelectedAgent({\n                        \"Home.useEffect.cycleInterval\": (prev)=>(prev + 1) % agentExamples.length\n                    }[\"Home.useEffect.cycleInterval\"]);\n                }\n            }[\"Home.useEffect.cycleInterval\"], 8000) // Change every 8 seconds\n            ;\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(cycleInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        agentExamples.length\n    ]);\n    // Animate terminal when agent changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            let isCancelled = false;\n            const animateTerminal = {\n                \"Home.useEffect.animateTerminal\": async ()=>{\n                    const example = agentExamples[selectedAgent];\n                    // Clear terminal\n                    setTerminalText('');\n                    if (isCancelled) return;\n                    await new Promise({\n                        \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"Home.useEffect.animateTerminal\"]);\n                    // Build the full terminal text\n                    let fullText = '> User: \"'.concat(example.input, '\"\\n');\n                    // Type input\n                    if (!isCancelled) {\n                        await typewriterEffect(fullText);\n                        await new Promise({\n                            \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 500)\n                        }[\"Home.useEffect.animateTerminal\"]);\n                    }\n                    // Add and type process lines\n                    for (const line of example.process){\n                        if (isCancelled) return;\n                        fullText += line + '\\n';\n                        await typewriterEffect(fullText, fullText.length - line.length - 1);\n                        await new Promise({\n                            \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 300)\n                        }[\"Home.useEffect.animateTerminal\"]);\n                    }\n                    if (isCancelled) return;\n                    await new Promise({\n                        \"Home.useEffect.animateTerminal\": (resolve)=>setTimeout(resolve, 500)\n                    }[\"Home.useEffect.animateTerminal\"]);\n                    // Add and type output\n                    fullText += \"> Result: \".concat(example.output);\n                    if (!isCancelled) {\n                        await typewriterEffect(fullText, fullText.length - example.output.length - 10);\n                    }\n                }\n            }[\"Home.useEffect.animateTerminal\"];\n            animateTerminal();\n            return ({\n                \"Home.useEffect\": ()=>{\n                    isCancelled = true;\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        selectedAgent\n    ]);\n    const validateEmail = (email)=>{\n        const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return re.test(email);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (!validateEmail(email)) {\n            setError('Please enter a valid email address');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/subscribe', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Something went wrong');\n            }\n            setSuccess(true);\n            setEmail('');\n            // Redirect to chat after 2 seconds\n            setTimeout(()=>{\n                window.location.href = '/chat';\n            }, 2000);\n        } catch (err) {\n            setError('Something went wrong. Please try again.');\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-black text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-black via-black to-gray-950\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute -top-1/2 -left-1/2 w-[200%] h-[200%] opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:100px_100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl font-bold\",\n                                children: \"AI Agents\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/build\",\n                                className: \"px-4 py-2 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors text-sm\",\n                                children: \"Start Building\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                ref: containerRef,\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    style: {\n                        opacity,\n                        scale\n                    },\n                    className: \"w-full max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 1\n                            },\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tighter mb-6\",\n                                    children: [\n                                        \"Build AI Agents\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-3xl sm:text-4xl md:text-5xl lg:text-6xl mt-4 h-[1.2em]\",\n                                            children: [\n                                                withoutCodeText.length > 8 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/70\",\n                                                            children: withoutCodeText.slice(0, 8)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: withoutCodeText.slice(8)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: withoutCodeText\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                withoutCodeText.length < 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                    animate: {\n                                                        opacity: [\n                                                            1,\n                                                            0\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 0.8,\n                                                        repeat: Infinity,\n                                                        repeatType: \"reverse\"\n                                                    },\n                                                    className: \"inline-block w-[3px] h-[0.9em] bg-white/60 ml-1 align-text-bottom\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl md:text-2xl text-white/50 max-w-3xl mx-auto\",\n                                    children: \"Transform your business with intelligent automation. No developers needed. Just drag, drop, and deploy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-[360px] sm:h-[500px] flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"absolute inset-0\",\n                                    children: [\n                                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-x-0 top-[20%] bottom-[20%] left-1/2 -translate-x-1/2 w-0.5 pointer-events-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                                                    animate: {\n                                                        y: [\n                                                            '-100%',\n                                                            '200%'\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"linear\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8 sm:mb-16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-white/80 sm:text-white/90\",\n                                                children: capabilities[currentCapability].title\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex flex-col sm:flex-row items-center justify-between max-w-5xl mx-auto px-4 sm:px-8 gap-2 sm:gap-0\",\n                                            children: capabilities[currentCapability].flow.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            initial: {\n                                                                scale: 0,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: activeFlow >= index ? 1 : 0,\n                                                                opacity: activeFlow >= index ? 1 : 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5,\n                                                                type: \"spring\",\n                                                                stiffness: 200\n                                                            },\n                                                            className: \"relative\",\n                                                            children: [\n                                                                activeFlow === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: index === 0 ? 0 : 0.6\n                                                                    },\n                                                                    className: \"absolute inset-0 rounded-xl overflow-hidden\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                            animate: {\n                                                                                opacity: [\n                                                                                    0.3,\n                                                                                    0.6,\n                                                                                    0.3\n                                                                                ],\n                                                                                scale: [\n                                                                                    1,\n                                                                                    1.05,\n                                                                                    1\n                                                                                ]\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 2,\n                                                                                repeat: Infinity,\n                                                                                ease: \"easeInOut\"\n                                                                            },\n                                                                            className: \"absolute inset-0 rounded-xl border border-white/40\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                            animate: {\n                                                                                rotate: [\n                                                                                    0,\n                                                                                    360\n                                                                                ]\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 4,\n                                                                                repeat: Infinity,\n                                                                                ease: \"linear\"\n                                                                            },\n                                                                            className: \"absolute inset-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                                style: {\n                                                                                    background: 'conic-gradient(from 0deg, transparent 70%, rgba(255,255,255,0.3), transparent 80%)',\n                                                                                    maskImage: 'radial-gradient(ellipse at center, transparent 30%, black 70%)'\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black backdrop-blur-sm border rounded-xl px-4 sm:px-6 py-2.5 sm:py-4 min-w-[120px] sm:min-w-[150px] text-center transition-all duration-300 \".concat(activeFlow === index ? 'border-white/40' : 'border-white/20'),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs sm:text-sm font-medium text-white/80 relative z-10\",\n                                                                        children: step\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        index < capabilities[currentCapability].flow.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            animate: {\n                                                                scale: activeFlow > index ? 1 : 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: 0.2,\n                                                                ease: \"easeOut\"\n                                                            },\n                                                            className: \"relative h-6 w-0.5 sm:h-px sm:w-auto sm:flex-1 mx-2\",\n                                                            style: {\n                                                                transformOrigin: isMobile ? 'top center' : 'left center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-b sm:bg-gradient-to-r from-white/10 to-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                activeFlow === index + 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                        initial: isMobile ? {\n                                                                            y: '-100%'\n                                                                        } : {\n                                                                            x: '-100%'\n                                                                        },\n                                                                        animate: isMobile ? {\n                                                                            y: [\n                                                                                '0%',\n                                                                                '200%'\n                                                                            ]\n                                                                        } : {\n                                                                            x: [\n                                                                                '0%',\n                                                                                '200%'\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.8,\n                                                                            ease: \"easeOut\",\n                                                                            times: [\n                                                                                0,\n                                                                                1\n                                                                            ]\n                                                                        },\n                                                                        className: \"absolute \".concat(isMobile ? 'w-full h-12' : 'h-full w-12'),\n                                                                        style: {\n                                                                            background: isMobile ? 'linear-gradient(to bottom, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)' : 'linear-gradient(to right, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: activeFlow === 3 ? 1 : 0,\n                                                y: activeFlow === 3 ? 0 : 20\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            className: \"absolute -bottom-12 sm:-bottom-16 left-0 right-0 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl md:text-2xl font-bold text-white/70 sm:text-white/90\",\n                                                children: capabilities[currentCapability].metric\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, currentCapability, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 2\n                            },\n                            className: \"absolute bottom-8 left-1/2 -translate-x-1/2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.svg, {\n                                animate: {\n                                    y: [\n                                        0,\n                                        8,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                },\n                                className: \"w-5 h-5 text-white/20\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-[200px] pointer-events-none overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-1/2 -translate-x-1/2 w-0.5 h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                            animate: {\n                                y: [\n                                    '-100%',\n                                    '200%'\n                                ]\n                            },\n                            transition: {\n                                duration: 3,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 549,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"w-full max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl sm:text-5xl md:text-6xl font-bold mb-6\",\n                                    children: \"How AI Agents Work\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-white/50 max-w-3xl mx-auto\",\n                                    children: \"Watch AI agents process requests in real-time. No code required.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -50\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 px-4 py-3 border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-white/40 ml-auto\",\n                                                            children: \"AI Agent Terminal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: terminalRef,\n                                                    className: \"p-4 sm:p-6 font-mono text-xs sm:text-sm min-h-[300px] sm:min-h-[400px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-white/80 whitespace-pre-wrap\",\n                                                        children: [\n                                                            terminalText,\n                                                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex gap-2 flex-wrap\",\n                                            children: agentExamples.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedAgent(index),\n                                                    className: \"px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 \".concat(selectedAgent === index ? 'bg-white/10 text-white/90 border border-white/20' : 'bg-white/5 text-white/40 border border-white/10 hover:bg-white/10'),\n                                                    children: agent.name\n                                                }, index, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: isMobile ? 0 : 50,\n                                        y: isMobile ? 50 : 0\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative mt-8 lg:mt-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 -translate-x-1/2 w-0.5 h-full pointer-events-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent\",\n                                                            animate: {\n                                                                y: [\n                                                                    '-100%',\n                                                                    '200%'\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 3,\n                                                                repeat: Infinity,\n                                                                ease: \"linear\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: \"linear-gradient(45deg, transparent, white, transparent)\",\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 0\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"User Input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Natural language request\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: \"linear-gradient(45deg, transparent, white, transparent)\",\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 0.5\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white/60\",\n                                                                                fill: \"none\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                stroke: \"currentColor\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 1.5,\n                                                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                    lineNumber: 754,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 753,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                                animate: {\n                                                                                    rotate: 360\n                                                                                },\n                                                                                transition: {\n                                                                                    duration: 3,\n                                                                                    repeat: Infinity,\n                                                                                    ease: \"linear\"\n                                                                                },\n                                                                                className: \"absolute inset-0 rounded-lg\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-white/60 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                    lineNumber: 762,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"AI Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Understanding intent\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: \"linear-gradient(45deg, transparent, white, transparent)\",\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 1\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"Actions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 818,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Automated execution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.02,\n                                                        x: 5\n                                                    },\n                                                    className: \"relative z-10\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                className: \"absolute inset-0 rounded-xl\",\n                                                                style: {\n                                                                    background: \"linear-gradient(45deg, transparent, white, transparent)\",\n                                                                    backgroundSize: '200% 200%'\n                                                                },\n                                                                animate: {\n                                                                    backgroundPosition: [\n                                                                        '0% 0%',\n                                                                        '100% 100%',\n                                                                        '0% 0%'\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 3,\n                                                                    repeat: Infinity,\n                                                                    delay: 1.5\n                                                                },\n                                                                initial: {\n                                                                    opacity: 0\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-white/60\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 1.5,\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 864,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold mb-1 text-white/90\",\n                                                                                children: \"Results\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 870,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-white/50\",\n                                                                                children: \"Value delivered\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/30\",\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-2 sm:gap-4 mt-8 sm:mt-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"100ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Response Time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"∞\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Scale Limit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    whileHover: {\n                                                        y: -2\n                                                    },\n                                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg sm:text-2xl font-bold text-white/80\",\n                                                            children: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[10px] sm:text-xs text-white/40 mt-1\",\n                                                            children: \"Code Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"w-full max-w-xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-12 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        scale: 0\n                                    },\n                                    whileInView: {\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-4xl font-bold text-white/90\",\n                                            children: \"24/7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/40\",\n                                            children: \"Agent Building\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-12 bg-white/10\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        scale: 0\n                                    },\n                                    whileInView: {\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-4xl font-bold text-white/90\",\n                                            children: \"100%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/40\",\n                                            children: \"Done For You\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold mb-6\",\n                                    children: \"Type What You Want\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-white/50 mb-12\",\n                                    children: \"Our agents build. You deploy instantly.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: email,\n                                                    onChange: (e)=>{\n                                                        setEmail(e.target.value);\n                                                        setError('');\n                                                    },\n                                                    placeholder: \"Enter your email\",\n                                                    className: \"w-full px-6 py-4 text-lg bg-white/[0.03] border border-white/10 rounded-xl text-white outline-none transition-all duration-300 placeholder:text-white/30 hover:bg-white/[0.05] focus:bg-white/[0.07] focus:border-white/20\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    className: \"absolute -bottom-6 left-0 text-red-500 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: loading || success,\n                                            className: \"w-full px-8 py-4 text-lg font-semibold bg-white text-black rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(255,255,255,0.2)] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10\",\n                                                    children: loading ? 'Joining...' : success ? 'Welcome aboard!' : 'Get Early Access'\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 947,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            whileInView: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"grid grid-cols-3 gap-8 mt-20\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Instant Deploy\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Infinite Scale\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                        lineNumber: 1024,\n                                        columnNumber: 19\n                                    }, this),\n                                    title: \"Zero Code\"\n                                }\n                            ].map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center\",\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        type: \"spring\",\n                                        stiffness: 300\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex p-3 rounded-xl bg-white/[0.03] text-white/40 mb-3 group-hover:text-white/60 transition-colors\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/50\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                            lineNumber: 998,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 913,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                className: \"fixed inset-0 bg-black/90 backdrop-blur-md flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        type: \"spring\",\n                        duration: 0.5\n                    },\n                    className: \"bg-white text-black px-8 py-6 rounded-2xl font-semibold text-lg\",\n                    children: \"Welcome aboard! Redirecting you to the community...\"\n                }, void 0, false, {\n                    fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                    lineNumber: 1054,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n                lineNumber: 1049,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/AI Project /course/ai-agents-landing/app/page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"0rsuffjEPvkOuOcVtEyWqPzkYUc=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});