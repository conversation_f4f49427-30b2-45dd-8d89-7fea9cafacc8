# <PERSON>'s Simple Process Guide

## 🎯 When User Asks for a New Feature

Follow these steps in order:

### 1. Check Project Rules
First, read `CLAUDE.md` to understand:
- Project conventions
- Code structure rules  
- Testing requirements
- What NOT to do

### 2. Create Feature Request
Edit `INITIAL.md` with:
```markdown
## FEATURE
[What the user wants built - be specific]

## EXAMPLES  
[Look in examples/ folder for similar patterns]

## DOCUMENTATION
[Any relevant API docs, libraries, resources]

## OTHER CONSIDERATIONS
[Special requirements, constraints, gotchas]
```

### 3. Generate the Blueprint
Run command:
```bash
/generate-prp INITIAL.md
```
This creates a detailed implementation plan in `PRPs/` folder

### 4. Execute the Plan
Run command:
```bash
/execute-prp PRPs/[feature-name].md
```
This implements the feature with validation checks

## 📋 Quick Checklist

Before starting ANY feature:
- [ ] Read CLAUDE.md for project rules
- [ ] Check examples/ folder for patterns
- [ ] Create proper INITIAL.md request
- [ ] Generate PRP (don't skip this!)
- [ ] Execute PRP with validation

## 🚫 Never Do This

- Don't code without a PRP
- Don't skip validation steps
- Don't delete existing code without permission
- Don't hallucinate libraries
- Don't exceed 500 lines per file

## 💡 Remember

**Context > Clever Prompts**
- PRPs contain all the context you need
- Follow the plan exactly
- Run all validation tests
- If tests fail, fix and retry

**The Formula:**
1. User Request → INITIAL.md
2. INITIAL.md → PRP (blueprint)
3. PRP → Working Code (with tests)

## 🆘 If Stuck

1. Re-read the PRP - it has all answers
2. Check examples/ folder for patterns
3. Look at validation criteria
4. Ask user for clarification

---

**Golden Rule:** Never start coding without a PRP. The PRP is your blueprint - it has researched the codebase, found patterns, and created a validated plan. Trust the process!