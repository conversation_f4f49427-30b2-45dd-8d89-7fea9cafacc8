# Trading Co-pilot Project Overview

## 🎯 Project Vision

An AI-powered trading assistant that provides real-time market analysis, portfolio optimization, and trading insights through an intuitive chat interface integrated with the AG-UI protocol.

## 🏗️ Architecture Overview

### Tech Stack
```yaml
Framework: Next.js 15.2.4 (App Router)
Language: TypeScript 5.x
UI Library: React 19
Styling: Tailwind CSS + shadcn/ui
Authentication: Supabase Auth
AI Protocol: AG-UI (Agentic UI)
State Management: React Hooks + Context
Real-time: SSE (Server-Sent Events)
Package Manager: pnpm
```

### Core Technologies
- **Next.js 15**: Latest App Router for optimal performance
- **AG-UI Protocol**: Event-driven AI agent communication
- **Supabase**: Authentication and future data persistence
- **shadcn/ui**: Composable UI components
- **React 19**: Latest React features including use() hook

## 📁 Project Structure

```
realfrontend/
├── .claude/                    # Claude assistant configuration
│   ├── CLAUDE.md              # Core capabilities & commitments
│   ├── PROJECT.md             # This file
│   ├── STANDARDS.md           # Coding standards
│   ├── WORKFLOWS.md           # Common workflows
│   ├── reference/             # Technology references
│   ├── scripts/               # Automation scripts
│   ├── technologies/          # Learning docs
│   └── context/               # Project context
├── app/                       # Next.js App Router
│   ├── (auth)/               # Auth group routes
│   ├── api/                  # API endpoints
│   ├── layout.tsx            # Root layout
│   └── page.tsx              # Home page
├── components/               # React components
│   ├── ui/                   # shadcn/ui primitives
│   ├── agui-message.tsx      # AG-UI message renderer
│   ├── artifact-panel.tsx    # Artifact display
│   ├── trading-copilot.tsx   # Main chat interface
│   └── ...                   # Other components
├── contexts/                 # React contexts
│   └── auth-context.tsx      # Authentication state
├── hooks/                    # Custom React hooks
│   └── useAGUIChat.ts       # AG-UI chat integration
├── lib/                      # Utility libraries
│   └── supabase/            # Supabase clients
├── types/                    # TypeScript types
│   └── agui.ts              # AG-UI protocol types
└── ag-ui/                    # AG-UI SDK reference

```

## 🔑 Key Components

### 1. **TradingCopilot** (`/components/trading-copilot.tsx`)
- Main chat interface
- File upload support
- Session management
- Real-time streaming

### 2. **useAGUIChat Hook** (`/hooks/useAGUIChat.ts`)
- SSE connection management
- Event stream processing
- Message state management
- Artifact streaming

### 3. **AGUIMessage** (`/components/agui-message.tsx`)
- Message rendering
- Thinking section display
- Code block highlighting
- Artifact previews

### 4. **ArtifactPanel** (`/components/artifact-panel.tsx`)
- Streaming artifact display
- Code/JSON/Markdown rendering
- Download/Copy functionality
- Collapsible interface

### 5. **Authentication** (`/contexts/auth-context.tsx`)
- Supabase integration
- OAuth providers (GitHub, Google)
- Protected routes
- Session management

## 🔄 Data Flow

```mermaid
graph TD
    A[User Input] --> B[TradingCopilot Component]
    B --> C[useAGUIChat Hook]
    C --> D[AG-UI Backend]
    D --> E[SSE Event Stream]
    E --> F[Event Processing]
    F --> G[State Updates]
    G --> H[UI Rendering]
    H --> I[AGUIMessage]
    H --> J[ArtifactPanel]
```

## 🌟 Current Features

### Implemented
- ✅ Real-time AI chat interface
- ✅ AG-UI protocol integration
- ✅ File upload support (CSV, JSON, TXT, PDF)
- ✅ Artifact streaming panel
- ✅ Authentication system
- ✅ Dark theme UI
- ✅ Chat session management
- ✅ Thinking section display
- ✅ Code syntax highlighting

### In Progress
- 🔄 Chart/Table visualization in artifacts
- 🔄 Voice input support
- 🔄 Trading metrics dashboard
- 🔄 Portfolio integration

### Planned
- 📋 Real-time market data integration
- 📋 Trading execution capabilities
- 📋 Advanced charting tools
- 📋 Multi-agent collaboration
- 📋 Mobile app

## 🔗 API Endpoints

### Internal APIs
- `/api/test-artifacts` - Test artifact streaming
- `/api/waitlist` - Waitlist registration
- `/auth/callback` - OAuth callback

### External APIs
- `${NEXT_PUBLIC_API_URL}/v1/runs/stream` - AG-UI streaming
- `${NEXT_PUBLIC_API_URL}/v1/files/upload` - File uploads

## 🔐 Environment Variables

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=

# AG-UI Backend
NEXT_PUBLIC_API_URL=http://localhost:8002

# App Config
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Run development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

## 🎨 Design System

### Colors
- Background: `#131314` (main), `#1e1e1f` (elevated)
- Borders: `#2a2a2a`
- Text: `white` (primary), `#8e8ea0` (secondary)
- Accent: Blue-Violet gradient

### Typography
- Font: System default
- Sizes: Responsive with Tailwind
- Weights: Normal (400), Medium (500), Semibold (600)

### Components
- All UI primitives from shadcn/ui
- Custom themed for dark mode
- Consistent spacing and animations

## 🔍 Key Patterns

### 1. **Event-Driven Architecture**
- AG-UI events drive UI updates
- Streaming-first approach
- Real-time feedback

### 2. **Component Composition**
- Small, focused components
- Composable UI primitives
- Prop drilling minimized

### 3. **Type Safety**
- Full TypeScript coverage
- Strict type checking
- AG-UI protocol types

### 4. **Performance First**
- SSR where beneficial
- Client components for interactivity
- Optimized re-renders

## 🎯 Project Goals

1. **Best-in-class UX** for AI-powered trading
2. **Real-time performance** with streaming
3. **Enterprise-grade security** and reliability
4. **Extensible architecture** for future features
5. **Developer experience** excellence

---

> This project represents the future of AI-assisted trading - where sophisticated analysis meets intuitive design.