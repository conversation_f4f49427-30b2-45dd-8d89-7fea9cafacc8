# Project Decisions & Context

> Important architectural decisions, trade-offs, and context for the Trading Co-pilot project.

## 🎯 Project Vision

**Goal**: Build the most intuitive and powerful AI-powered trading assistant that democratizes professional-grade trading analysis.

**Key Differentiators**:
- Real-time streaming AI responses
- Visual artifacts for data analysis
- File upload for statement analysis
- Professional dark theme UI
- Enterprise-grade architecture

## 🏗️ Architectural Decisions

### 1. **Next.js 15 with App Router**
**Decision**: Use Next.js 15 App Router instead of Pages Router

**Rationale**:
- Better performance with React Server Components
- Streaming SSR capabilities align with AG-UI protocol
- Simplified data fetching patterns
- Future-proof architecture

**Trade-offs**:
- Steeper learning curve
- Some libraries not yet compatible
- More complex mental model

### 2. **AG-UI Protocol Integration**
**Decision**: Build on top of AG-UI protocol for AI communication

**Rationale**:
- Standardized event-driven architecture
- Real-time streaming capabilities
- Extensible for future features
- Clear separation of concerns

**Implementation Details**:
- Custom `useAGUIChat` hook for state management
- SSE for unidirectional streaming
- Event-based message construction

### 3. **Supabase for Authentication**
**Decision**: Use Supabase instead of custom auth

**Rationale**:
- Battle-tested authentication system
- Built-in OAuth providers
- Row-level security for future features
- Scales from MVP to enterprise

**Future Benefits**:
- Ready for real-time features
- Database for user preferences
- File storage capabilities
- Analytics and monitoring

### 4. **Client-Side Heavy Architecture**
**Decision**: Main interface is client-side with "use client"

**Rationale**:
- Real-time interactions require client state
- SSE connections must be client-side
- Rich interactivity needs React hooks
- Better UX for chat interface

**Optimizations**:
- Lazy load heavy components
- Use React.memo strategically
- Implement virtual scrolling for messages

### 5. **TypeScript Strict Mode (Future)**
**Decision**: Currently relaxed, plan to enable strict mode

**Current State**:
```json
{
  "typescript": {
    "ignoreBuildErrors": true
  }
}
```

**Migration Plan**:
1. Fix existing type errors
2. Enable strict mode incrementally
3. Add type tests to CI/CD

## 🎨 Design Decisions

### 1. **Dark Theme Only**
**Decision**: Launch with dark theme, no light theme initially

**Rationale**:
- Target audience (traders) prefer dark themes
- Reduces eye strain during extended use
- Focuses development effort
- Matches professional trading platforms

### 2. **Artifact Panel Design**
**Decision**: Sliding panel on right side

**Rationale**:
- Keeps main chat visible
- Natural reading flow (left to right)
- Can be hidden when not needed
- Supports multiple artifacts

### 3. **No Avatar Customization**
**Decision**: Fixed gradient avatars

**Rationale**:
- Reduces complexity
- Consistent branding
- Focus on functionality
- Can add later if needed

## 🔧 Technical Decisions

### 1. **No State Management Library**
**Decision**: Use React hooks instead of Redux/Zustand

**Rationale**:
- Simpler architecture
- Sufficient for current needs
- Less boilerplate
- Easier onboarding

**Future Consideration**:
- Add Zustand if state gets complex
- Consider TanStack Query for server state

### 2. **shadcn/ui Components**
**Decision**: Copy components vs. component library

**Rationale**:
- Full control over components
- No version lock-in
- Can customize freely
- Smaller bundle size

### 3. **File Upload Strategy**
**Decision**: Support local processing with fallback

**Current Implementation**:
```typescript
// Try API upload first
try {
  await uploadToAPI(file)
} catch {
  // Fallback to base64 embedding
  const base64 = await fileToBase64(file)
  sendMessage(content, { file: base64 })
}
```

## 🚀 Performance Decisions

### 1. **Message Virtualization (Planned)**
**Decision**: Implement react-window for long conversations

**Trigger**: When conversations exceed 100 messages

**Implementation**:
```typescript
import { FixedSizeList } from 'react-window'
```

### 2. **Code Splitting Strategy**
**Decision**: Lazy load heavy components

**Current Implementations**:
- Chart components
- File processing utilities
- Analytics modules

### 3. **Image Optimization Disabled**
**Decision**: Disabled Next.js image optimization

**Rationale**:
- Simplifies deployment
- No external image sources currently
- Can enable when needed

## 🔒 Security Decisions

### 1. **Environment Variables**
**Decision**: Use NEXT_PUBLIC_ prefix appropriately

**Rule**: Only non-sensitive data in NEXT_PUBLIC_ variables

### 2. **CORS Configuration**
**Decision**: Permissive CORS for development

**Production TODO**:
- Restrict to specific origins
- Implement CSRF protection
- Add rate limiting

### 3. **Input Validation**
**Decision**: Validate on client, verify on server

**Implementation**:
- Zod schemas for type safety
- Server-side validation in API routes
- Sanitize user inputs

## 📈 Scaling Considerations

### 1. **Database Schema (Future)**
```sql
-- Planned schema
users
conversations
messages
artifacts
trading_accounts
analysis_results
```

### 2. **Caching Strategy**
- Browser cache for static assets
- SWR for API responses
- LocalStorage for user preferences
- IndexedDB for offline support

### 3. **Multi-tenant Architecture**
- User isolation at auth level
- Separate AG-UI sessions per user
- Resource limits per user
- Usage tracking

## 🔄 Migration Paths

### 1. **From Test Artifacts to Real API**
Currently using `/api/test-artifacts` for development

**Migration Steps**:
1. Deploy AG-UI backend
2. Update API_URL environment variable
3. Remove test endpoint
4. Add error handling for production

### 2. **Adding Real-time Features**
**Planned Features**:
- Live market data streaming
- Collaborative analysis
- Real-time notifications

**Architecture Ready**:
- SSE infrastructure in place
- Event-driven architecture
- WebSocket upgrade path clear

## 📋 Deferred Decisions

### 1. **Mobile App**
- React Native vs Flutter
- Code sharing strategy
- Offline capabilities

### 2. **Monetization**
- Subscription tiers
- Usage-based pricing
- Feature gating

### 3. **AI Model Selection**
- Multiple model support
- User choice of models
- Cost optimization

## 🎯 Success Metrics

### Technical
- < 100ms interaction latency
- < 3s initial load time
- 99.9% uptime
- < 1% error rate

### User Experience
- < 30s to first insight
- > 80% task completion rate
- > 4.5/5 user satisfaction
- < 2 clicks to any feature

---

> These decisions form the foundation of our architecture. They should be revisited quarterly and updated as we learn more about our users and scale.