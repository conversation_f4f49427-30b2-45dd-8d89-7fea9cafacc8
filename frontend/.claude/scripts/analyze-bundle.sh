#!/bin/bash

# Bundle Analysis Script
# Analyzes the Next.js bundle size and provides insights

echo "🔍 Analyzing Bundle Size..."

# Build the project
echo "📦 Building project..."
pnpm build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix build errors first."
    exit 1
fi

# Analyze bundle using Next.js built-in analyzer
echo "📊 Generating bundle analysis..."

# Install bundle analyzer if not present
if ! npm list @next/bundle-analyzer > /dev/null 2>&1; then
    echo "📥 Installing bundle analyzer..."
    pnpm add -D @next/bundle-analyzer
fi

# Create temporary next.config for analysis
cat > next.config.analyze.mjs << 'EOF'
import bundleAnalyzer from '@next/bundle-analyzer'

const withBundleAnalyzer = bundleAnalyzer({
  enabled: true,
  openAnalyzer: true
})

export default withBundleAnalyzer({
  // Your existing config
  images: {
    unoptimized: true
  },
  // Ignore TypeScript and ESLint errors during build
  typescript: {
    ignoreBuildErrors: true
  },
  eslint: {
    ignoreDuringBuilds: true
  }
})
EOF

# Run build with analyzer
ANALYZE=true pnpm next build --config next.config.analyze.mjs

# Cleanup
rm next.config.analyze.mjs

echo "✅ Bundle analysis complete!"
echo "💡 Tips for reducing bundle size:"
echo "   - Use dynamic imports for heavy components"
echo "   - Check for duplicate dependencies"
echo "   - Tree-shake unused exports"
echo "   - Lazy load non-critical features"