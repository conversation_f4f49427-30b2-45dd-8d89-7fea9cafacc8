#!/bin/bash

# Quick Start Script
# Get up and running with the Trading Co-pilot project

echo "🚀 Trading Co-pilot Quick Start"
echo "==============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Check prerequisites
echo -e "\n${BLUE}Checking prerequisites...${NC}"

if ! command_exists node; then
    echo "❌ Node.js not found. Please install Node.js 18+"
    exit 1
fi

if ! command_exists pnpm; then
    echo "📦 Installing pnpm..."
    npm install -g pnpm
fi

# 2. Install dependencies
echo -e "\n${BLUE}Installing dependencies...${NC}"
pnpm install

# 3. Check for .env.local
echo -e "\n${BLUE}Checking environment setup...${NC}"
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local file..."
    cat > .env.local << 'EOF'
# Supabase (required for auth)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# AG-UI Backend (optional - uses test endpoint if not set)
NEXT_PUBLIC_API_URL=http://localhost:8002

# App URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
EOF
    echo -e "${YELLOW}⚠️  Please update .env.local with your Supabase credentials${NC}"
fi

# 4. Run health check
echo -e "\n${BLUE}Running health check...${NC}"
./.claude/scripts/health-check.sh

# 5. Start development server
echo -e "\n${GREEN}✅ Setup complete!${NC}"
echo -e "\n${BLUE}Starting development server...${NC}"
echo -e "${YELLOW}Opening http://localhost:3000 in your browser...${NC}\n"

# Open browser (works on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    sleep 2 && open http://localhost:3000 &
fi

# Start dev server
pnpm dev