#!/bin/bash

# Type Checking Script
# Comprehensive TypeScript validation with detailed reporting

echo "🔍 TypeScript Type Checking..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if TypeScript is installed
if ! command -v tsc &> /dev/null; then
    echo "❌ TypeScript not found. Installing..."
    pnpm add -D typescript
fi

# Run type checking
echo "📋 Running TypeScript compiler..."
npx tsc --noEmit --pretty 2>&1 | tee .claude/type-check-results.txt

# Check if there were errors
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo -e "${GREEN}✅ No TypeScript errors found!${NC}"
    rm -f .claude/type-check-results.txt
else
    echo -e "${RED}❌ TypeScript errors detected${NC}"
    
    # Count errors
    ERROR_COUNT=$(grep -c "error TS" .claude/type-check-results.txt || echo "0")
    echo -e "${YELLOW}Found $ERROR_COUNT TypeScript errors${NC}"
    
    # Show summary of error types
    echo -e "\n${BLUE}Error Summary:${NC}"
    grep "error TS" .claude/type-check-results.txt | sed 's/.*error TS\([0-9]*\).*/\1/' | sort | uniq -c | sort -nr | head -10 | while read count code; do
        case $code in
            2322) echo "  $count × TS2322: Type assignment errors" ;;
            2339) echo "  $count × TS2339: Property does not exist" ;;
            2345) echo "  $count × TS2345: Argument type mismatch" ;;
            2304) echo "  $count × TS2304: Cannot find name" ;;
            7006) echo "  $count × TS7006: Parameter implicitly has 'any' type" ;;
            *) echo "  $count × TS$code" ;;
        esac
    done
    
    echo -e "\n${YELLOW}Full results saved to: .claude/type-check-results.txt${NC}"
    echo -e "${BLUE}Common fixes:${NC}"
    echo "  • Add explicit types to function parameters"
    echo "  • Check imports and module declarations"
    echo "  • Ensure interfaces match implementations"
    echo "  • Use type assertions where appropriate"
    
    exit 1
fi

# Additional strict checks
echo -e "\n${BLUE}Running strict mode checks...${NC}"
npx tsc --noEmit --strict --pretty 2>&1 | tee .claude/strict-check-results.txt

if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo -e "${GREEN}✅ Passes strict mode!${NC}"
    rm -f .claude/strict-check-results.txt
else
    echo -e "${YELLOW}⚠️  Some issues in strict mode (optional)${NC}"
    echo "Consider enabling strict mode for better type safety"
fi