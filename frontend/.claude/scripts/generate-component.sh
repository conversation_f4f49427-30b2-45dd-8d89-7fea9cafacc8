#!/bin/bash

# Component Generator Script
# Quickly scaffold new components with tests and types

# Check if component name was provided
if [ -z "$1" ]; then
    echo "❌ Please provide a component name"
    echo "Usage: ./generate-component.sh ComponentName"
    exit 1
fi

COMPONENT_NAME=$1
COMPONENT_DIR="components"
COMPONENT_PATH="$COMPONENT_DIR/$COMPONENT_NAME"

# Convert PascalCase to kebab-case for file names
KEBAB_NAME=$(echo $COMPONENT_NAME | sed 's/\([a-z0-9]\)\([A-Z]\)/\1-\2/g' | tr '[:upper:]' '[:lower:]')

echo "🚀 Generating component: $COMPONENT_NAME"

# Create component directory
mkdir -p $COMPONENT_PATH

# Generate component file
cat > "$COMPONENT_PATH/$COMPONENT_NAME.tsx" << EOF
"use client"

import React from 'react'
import { cn } from '@/lib/utils'

export interface ${COMPONENT_NAME}Props {
  className?: string
  children?: React.ReactNode
}

export function $COMPONENT_NAME({ className, children, ...props }: ${COMPONENT_NAME}Props) {
  return (
    <div className={cn('', className)} {...props}>
      {children}
    </div>
  )
}
EOF

# Generate test file
cat > "$COMPONENT_PATH/$COMPONENT_NAME.test.tsx" << EOF
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { $COMPONENT_NAME } from './$COMPONENT_NAME'

describe('$COMPONENT_NAME', () => {
  it('should render children correctly', () => {
    render(
      <$COMPONENT_NAME>
        <span>Test Content</span>
      </$COMPONENT_NAME>
    )
    
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    const { container } = render(
      <$COMPONENT_NAME className="custom-class">
        Content
      </$COMPONENT_NAME>
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })
})
EOF

# Generate types file
cat > "$COMPONENT_PATH/$COMPONENT_NAME.types.ts" << EOF
// Types for $COMPONENT_NAME component

export interface ${COMPONENT_NAME}State {
  // Add state types here
}

export interface ${COMPONENT_NAME}Actions {
  // Add action types here
}
EOF

# Generate index file for easier imports
cat > "$COMPONENT_PATH/index.ts" << EOF
export { $COMPONENT_NAME } from './$COMPONENT_NAME'
export type { ${COMPONENT_NAME}Props } from './$COMPONENT_NAME'
EOF

# Generate Storybook story (if using Storybook)
cat > "$COMPONENT_PATH/$COMPONENT_NAME.stories.tsx" << EOF
import type { Meta, StoryObj } from '@storybook/react'
import { $COMPONENT_NAME } from './$COMPONENT_NAME'

const meta = {
  title: 'Components/$COMPONENT_NAME',
  component: $COMPONENT_NAME,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof $COMPONENT_NAME>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Default $COMPONENT_NAME',
  },
}

export const WithCustomClass: Story = {
  args: {
    children: 'Styled $COMPONENT_NAME',
    className: 'p-4 bg-blue-500 text-white rounded',
  },
}
EOF

echo "✅ Component generated successfully!"
echo ""
echo "📁 Created files:"
echo "  - $COMPONENT_PATH/$COMPONENT_NAME.tsx"
echo "  - $COMPONENT_PATH/$COMPONENT_NAME.test.tsx"
echo "  - $COMPONENT_PATH/$COMPONENT_NAME.types.ts"
echo "  - $COMPONENT_PATH/$COMPONENT_NAME.stories.tsx"
echo "  - $COMPONENT_PATH/index.ts"
echo ""
echo "📝 Next steps:"
echo "  1. Implement the component logic in $COMPONENT_NAME.tsx"
echo "  2. Add tests in $COMPONENT_NAME.test.tsx"
echo "  3. Import with: import { $COMPONENT_NAME } from '@/components/$COMPONENT_NAME'"