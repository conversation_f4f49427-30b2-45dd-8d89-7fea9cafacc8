#!/bin/bash

# Project Health Check Script
# Comprehensive check of project health and common issues

echo "🏥 Running Project Health Check..."
echo "================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ISSUES_FOUND=0

# Function to check command existence
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Check Node.js version
echo -e "\n${BLUE}1. Checking Node.js version...${NC}"
if command_exists node; then
    NODE_VERSION=$(node -v)
    echo -e "   Node.js version: $NODE_VERSION"
    
    # Check if version is 18 or higher
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1 | sed 's/v//')
    if [ $MAJOR_VERSION -lt 18 ]; then
        echo -e "   ${YELLOW}⚠️  Node.js 18+ recommended (found $NODE_VERSION)${NC}"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    else
        echo -e "   ${GREEN}✅ Node.js version OK${NC}"
    fi
else
    echo -e "   ${RED}❌ Node.js not found${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 2. Check package manager
echo -e "\n${BLUE}2. Checking package manager...${NC}"
if command_exists pnpm; then
    PNPM_VERSION=$(pnpm -v)
    echo -e "   ${GREEN}✅ pnpm version: $PNPM_VERSION${NC}"
else
    echo -e "   ${YELLOW}⚠️  pnpm not found. Install with: npm install -g pnpm${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 3. Check dependencies
echo -e "\n${BLUE}3. Checking dependencies...${NC}"
if [ -f "package.json" ]; then
    if [ -d "node_modules" ]; then
        echo -e "   ${GREEN}✅ Dependencies installed${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Dependencies not installed. Run: pnpm install${NC}"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
else
    echo -e "   ${RED}❌ package.json not found${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 4. Check for TypeScript errors
echo -e "\n${BLUE}4. Checking TypeScript...${NC}"
if command_exists tsc; then
    tsc --noEmit > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "   ${GREEN}✅ No TypeScript errors${NC}"
    else
        echo -e "   ${YELLOW}⚠️  TypeScript errors found. Run: ./claude/scripts/check-types.sh${NC}"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
else
    echo -e "   ${YELLOW}⚠️  TypeScript not found${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 5. Check ESLint
echo -e "\n${BLUE}5. Checking ESLint...${NC}"
if [ -f ".eslintrc.json" ] || [ -f "eslint.config.mjs" ]; then
    npx eslint . --max-warnings=0 > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "   ${GREEN}✅ No linting issues${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Linting issues found. Run: pnpm lint${NC}"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
else
    echo -e "   ${YELLOW}⚠️  ESLint configuration not found${NC}"
fi

# 6. Check environment variables
echo -e "\n${BLUE}6. Checking environment variables...${NC}"
ENV_FILE=".env.local"
REQUIRED_VARS=(
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"
    "NEXT_PUBLIC_API_URL"
)

if [ -f "$ENV_FILE" ]; then
    MISSING_VARS=()
    for var in "${REQUIRED_VARS[@]}"; do
        if ! grep -q "^$var=" "$ENV_FILE"; then
            MISSING_VARS+=($var)
        fi
    done
    
    if [ ${#MISSING_VARS[@]} -eq 0 ]; then
        echo -e "   ${GREEN}✅ All required environment variables present${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Missing environment variables:${NC}"
        for var in "${MISSING_VARS[@]}"; do
            echo "      - $var"
        done
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
else
    echo -e "   ${YELLOW}⚠️  .env.local file not found${NC}"
    echo "   Create one with:"
    echo "   cp .env.example .env.local"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 7. Check build
echo -e "\n${BLUE}7. Checking production build...${NC}"
echo "   Running build (this may take a moment)..."
pnpm build > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "   ${GREEN}✅ Build successful${NC}"
else
    echo -e "   ${RED}❌ Build failed. Run: pnpm build${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 8. Check for security vulnerabilities
echo -e "\n${BLUE}8. Checking for security vulnerabilities...${NC}"
pnpm audit --production > /dev/null 2>&1
AUDIT_EXIT_CODE=$?
if [ $AUDIT_EXIT_CODE -eq 0 ]; then
    echo -e "   ${GREEN}✅ No vulnerabilities found${NC}"
else
    echo -e "   ${YELLOW}⚠️  Security vulnerabilities found. Run: pnpm audit${NC}"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 9. Check git status
echo -e "\n${BLUE}9. Checking git status...${NC}"
if [ -d ".git" ]; then
    UNCOMMITTED=$(git status --porcelain | wc -l)
    if [ $UNCOMMITTED -eq 0 ]; then
        echo -e "   ${GREEN}✅ Working directory clean${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Uncommitted changes: $UNCOMMITTED files${NC}"
    fi
else
    echo -e "   ${YELLOW}⚠️  Not a git repository${NC}"
fi

# 10. Check for TODO comments
echo -e "\n${BLUE}10. Checking for TODO comments...${NC}"
TODO_COUNT=$(grep -r "TODO\|FIXME\|HACK" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . 2>/dev/null | wc -l)
if [ $TODO_COUNT -eq 0 ]; then
    echo -e "   ${GREEN}✅ No TODO comments found${NC}"
else
    echo -e "   ${YELLOW}📝 Found $TODO_COUNT TODO/FIXME/HACK comments${NC}"
fi

# Summary
echo -e "\n================================"
echo -e "${BLUE}Health Check Summary${NC}"
echo "================================"

if [ $ISSUES_FOUND -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Your project is healthy.${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  Found $ISSUES_FOUND issues that need attention.${NC}"
    echo -e "\n${BLUE}Quick fixes:${NC}"
    echo "1. Install dependencies: pnpm install"
    echo "2. Fix TypeScript errors: pnpm type-check"
    echo "3. Fix linting issues: pnpm lint --fix"
    echo "4. Update dependencies: pnpm update --interactive"
    exit 1
fi