# Coding Standards & Patterns

> Excellence in code is non-negotiable. These standards ensure consistency, maintainability, and scalability.

## 🎨 Code Style Guidelines

### TypeScript Standards

```typescript
// ✅ GOOD: Explicit types, descriptive names
interface TradingAnalysis {
  winRate: number
  profitFactor: number
  maxDrawdown: number
  metrics: TradingMetrics[]
}

// ❌ BAD: Any types, unclear names
interface Data {
  wr: any
  pf: any
  dd: any
  m: any[]
}
```

### Naming Conventions

```typescript
// Components: PascalCase
export function TradingCopilot() {}

// Hooks: camelCase with 'use' prefix
export function useAGUIChat() {}

// Constants: UPPER_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3

// Types/Interfaces: PascalCase with descriptive names
interface AGUIEventHandler {}
type MessageRole = 'user' | 'assistant'

// Files: kebab-case for components, camelCase for utils
// trading-copilot.tsx ✅
// useAGUIChat.ts ✅
```

### React Patterns

```typescript
// ✅ GOOD: Typed props, clear structure
interface ButtonProps {
  variant?: 'primary' | 'secondary'
  onClick?: () => void
  children: React.ReactNode
}

export function Button({ variant = 'primary', onClick, children }: ButtonProps) {
  return (
    <button className={cn('base-styles', variants[variant])} onClick={onClick}>
      {children}
    </button>
  )
}

// ❌ BAD: Untyped, mixed concerns
export function Button(props) {
  return <button {...props} />
}
```

## 🏗️ Architecture Patterns

### 1. **Component Structure**

```typescript
// Feature-based organization
components/
  trading-copilot/
    TradingCopilot.tsx       // Main component
    TradingCopilot.test.tsx  // Tests
    TradingCopilot.types.ts  // Types
    components/              // Sub-components
      MessageList.tsx
      InputArea.tsx
    hooks/                   // Component-specific hooks
      useMessages.ts
```

### 2. **Custom Hooks Pattern**

```typescript
// Encapsulate complex logic in custom hooks
function useAGUIChat(options: UseAGUIChatOptions) {
  // State management
  const [messages, setMessages] = useState<Message[]>([])
  const [isStreaming, setIsStreaming] = useState(false)
  
  // Refs for persistent values
  const abortControllerRef = useRef<AbortController>()
  
  // Callbacks with proper dependencies
  const sendMessage = useCallback(async (content: string) => {
    // Implementation
  }, [/* dependencies */])
  
  // Cleanup
  useEffect(() => {
    return () => {
      abortControllerRef.current?.abort()
    }
  }, [])
  
  // Return stable API
  return {
    messages,
    isStreaming,
    sendMessage
  }
}
```

### 3. **Error Handling**

```typescript
// Comprehensive error handling
async function fetchData() {
  try {
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  } catch (error) {
    // Log for debugging
    console.error('[fetchData] Error:', error)
    
    // User-friendly error
    if (error instanceof TypeError) {
      throw new Error('Network connection failed')
    }
    
    throw error
  }
}
```

### 4. **State Management**

```typescript
// Use proper state patterns
// ✅ GOOD: Grouped related state
interface ChatState {
  messages: Message[]
  isStreaming: boolean
  error: string | null
}

const [state, setState] = useState<ChatState>({
  messages: [],
  isStreaming: false,
  error: null
})

// ❌ BAD: Scattered state
const [messages, setMessages] = useState([])
const [isStreaming, setIsStreaming] = useState(false)
const [error, setError] = useState(null)
```

## 🔄 AG-UI Integration Patterns

### Event Processing

```typescript
// Centralized event handler with type safety
const handleAGUIEvent = useCallback((event: AGUIEventUnion) => {
  switch (event.event_type) {
    case AGUIEventType.TEXT_MESSAGE_START:
      handleMessageStart(event as TextMessageStartEvent)
      break
    
    case AGUIEventType.TEXT_MESSAGE_CONTENT:
      handleMessageContent(event as TextMessageContentEvent)
      break
    
    // Handle all event types
    default:
      console.warn('Unhandled event type:', event.event_type)
  }
}, [/* dependencies */])
```

### Streaming State Management

```typescript
// Use refs for streaming data to avoid re-renders
const streamingDataRef = useRef<Map<string, StreamingData>>(new Map())
const [displayData, setDisplayData] = useState<DisplayData[]>([])

// Update ref during streaming
streamingDataRef.current.set(id, data)

// Batch updates for display
const updateDisplay = useCallback(() => {
  setDisplayData(Array.from(streamingDataRef.current.values()))
}, [])
```

## 🎯 Performance Patterns

### 1. **Memoization**

```typescript
// Memoize expensive computations
const processedData = useMemo(() => {
  return heavyProcessing(rawData)
}, [rawData])

// Memoize callbacks
const handleClick = useCallback(() => {
  doSomething(id)
}, [id])

// Memoize components
const MemoizedComponent = React.memo(ExpensiveComponent)
```

### 2. **Lazy Loading**

```typescript
// Lazy load heavy components
const ChartPanel = lazy(() => import('./ChartPanel'))

// Use with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <ChartPanel data={chartData} />
</Suspense>
```

### 3. **Optimistic Updates**

```typescript
// Update UI immediately, sync later
const sendMessage = async (content: string) => {
  // Optimistic update
  const tempMessage = { id: tempId, content, status: 'sending' }
  setMessages(prev => [...prev, tempMessage])
  
  try {
    const response = await api.send(content)
    // Replace temp with real
    setMessages(prev => 
      prev.map(m => m.id === tempId ? response : m)
    )
  } catch (error) {
    // Rollback on error
    setMessages(prev => prev.filter(m => m.id !== tempId))
  }
}
```

## 🧪 Testing Standards

### Component Testing

```typescript
// Comprehensive component tests
describe('TradingCopilot', () => {
  it('should render initial state correctly', () => {
    render(<TradingCopilot />)
    expect(screen.getByText(/Hello, Trader/)).toBeInTheDocument()
  })
  
  it('should handle message sending', async () => {
    const { user } = render(<TradingCopilot />)
    
    await user.type(screen.getByPlaceholder(/Enter a prompt/), 'Test message')
    await user.click(screen.getByRole('button', { name: /send/i }))
    
    expect(screen.getByText('Test message')).toBeInTheDocument()
  })
})
```

### Hook Testing

```typescript
// Test hooks with renderHook
describe('useAGUIChat', () => {
  it('should initialize with empty messages', () => {
    const { result } = renderHook(() => useAGUIChat())
    expect(result.current.messages).toEqual([])
  })
})
```

## 🔒 Security Patterns

### 1. **Input Validation**

```typescript
// Always validate user input
const validateInput = (input: unknown): ValidatedInput => {
  const schema = z.object({
    message: z.string().min(1).max(1000),
    fileId: z.string().uuid().optional()
  })
  
  return schema.parse(input)
}
```

### 2. **XSS Prevention**

```typescript
// Use React's built-in XSS protection
// ✅ GOOD: React escapes by default
<div>{userContent}</div>

// ❌ BAD: Dangerous HTML
<div dangerouslySetInnerHTML={{ __html: userContent }} />
```

### 3. **Secure API Calls**

```typescript
// Include security headers
const secureFetch = async (url: string, options?: RequestInit) => {
  return fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': getCsrfToken(),
      ...options?.headers
    },
    credentials: 'include'
  })
}
```

## 📦 Import Organization

```typescript
// 1. External imports
import React, { useState, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { z } from 'zod'

// 2. Internal absolute imports
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'

// 3. Relative imports
import { TradingChart } from './components/TradingChart'
import { formatCurrency } from './utils'

// 4. Type imports
import type { Message, AGUIEvent } from '@/types/agui'
```

## 🚀 Optimization Checklist

- [ ] Use React.memo for expensive components
- [ ] Implement proper key props in lists
- [ ] Lazy load heavy components
- [ ] Optimize images with Next.js Image
- [ ] Use proper TypeScript types (avoid `any`)
- [ ] Implement error boundaries
- [ ] Add loading states
- [ ] Handle edge cases
- [ ] Write comprehensive tests
- [ ] Document complex logic

## 📋 Code Review Checklist

### Before Submitting
- [ ] TypeScript errors resolved
- [ ] ESLint warnings addressed
- [ ] Tests passing
- [ ] Performance optimized
- [ ] Security considered
- [ ] Documentation updated
- [ ] Accessibility checked
- [ ] Mobile responsive

### Review Focus
1. **Logic Correctness**: Does it work as intended?
2. **Code Quality**: Is it readable and maintainable?
3. **Performance**: Are there bottlenecks?
4. **Security**: Are there vulnerabilities?
5. **Standards**: Does it follow our patterns?

---

> "Code is read far more often than it is written. Write for your future self and your teammates."