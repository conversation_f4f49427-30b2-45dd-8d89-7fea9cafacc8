# Common Workflows & Tasks

> Streamlined workflows for maximum productivity. Each workflow is optimized for efficiency and quality.

## 🚀 Quick Commands

```bash
# Development
pnpm dev                    # Start dev server
pnpm build                  # Build for production
pnpm start                  # Start production server
pnpm lint                   # Run linting
pnpm type-check            # TypeScript validation

# Testing
pnpm test                  # Run tests
pnpm test:watch           # Watch mode
pnpm test:coverage        # Coverage report

# AG-UI Backend (if running locally)
cd backend && pnpm start   # Start AG-UI server
```

## 📋 Common Development Tasks

### 1. **Adding a New Feature**

```bash
# 1. Create feature branch
git checkout -b feature/your-feature-name

# 2. Scaffold component structure
# Use Claude: "Create a new component called XYZ with tests and types"

# 3. Implement feature with TDD
# - Write tests first
# - Implement functionality
# - Refactor for quality

# 4. Verify everything works
pnpm lint && pnpm type-check && pnpm test

# 5. Commit with conventional commits
git add .
git commit -m "feat: add XYZ feature"
```

### 2. **Creating a New Component**

```typescript
// Step 1: Define types
// types/your-component.ts
export interface YourComponentProps {
  // Props definition
}

// Step 2: Create component
// components/your-component.tsx
"use client"  // If needed

import { YourComponentProps } from '@/types/your-component'

export function YourComponent({ prop1, prop2 }: YourComponentProps) {
  return (
    // Implementation
  )
}

// Step 3: Add tests
// components/your-component.test.tsx
describe('YourComponent', () => {
  it('should render correctly', () => {
    // Test implementation
  })
})
```

### 3. **Integrating AG-UI Events**

```typescript
// Step 1: Add event type to types/agui.ts
export interface NewEventType extends AGUIEvent {
  event_type: AGUIEventType.NEW_EVENT
  // Event-specific fields
}

// Step 2: Update union type
export type AGUIEventUnion = 
  | ExistingEvents
  | NewEventType

// Step 3: Handle in useAGUIChat hook
case AGUIEventType.NEW_EVENT:
  const newEvent = event as NewEventType
  // Handle the event
  break
```

### 4. **Adding Authentication Routes**

```typescript
// Step 1: Create route structure
app/
  (protected)/
    dashboard/
      page.tsx

// Step 2: Add middleware check
// In page.tsx
export default async function DashboardPage() {
  // Auth is handled by middleware
  return <Dashboard />
}

// Step 3: Update middleware.ts if needed
export const config = {
  matcher: ['/(protected)/(.*)']
}
```

## 🔧 Advanced Workflows

### 1. **Performance Optimization**

```bash
# 1. Analyze bundle size
pnpm analyze

# 2. Identify large dependencies
# Look for opportunities to:
# - Lazy load components
# - Use dynamic imports
# - Tree-shake imports

# 3. Implement optimizations
# Example: Lazy load heavy component
const HeavyComponent = lazy(() => import('./HeavyComponent'))

# 4. Measure improvements
# Use Chrome DevTools Performance tab
```

### 2. **Debugging AG-UI Integration**

```typescript
// Enable debug logging
const DEBUG = true

const handleAGUIEvent = (event: AGUIEventUnion) => {
  if (DEBUG) {
    console.log('[AG-UI Event]', {
      type: event.event_type,
      sequence: event.sequence,
      timestamp: new Date(event.timestamp),
      data: event
    })
  }
  
  // Handle event
}

// Monitor SSE connection
onopen: (response) => {
  console.log('[SSE] Connection opened', response.status)
}

onerror: (error) => {
  console.error('[SSE] Error:', error)
}
```

### 3. **Database Schema Updates (Supabase)**

```sql
-- Step 1: Create migration file
-- supabase/migrations/TIMESTAMP_description.sql

-- Step 2: Write migration
CREATE TABLE IF NOT EXISTS trading_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  messages JSONB[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Apply migration
supabase db push

-- Step 4: Generate types
supabase gen types typescript --local > types/database.ts
```

## 🎯 Feature Implementation Patterns

### 1. **Real-time Features**

```typescript
// Pattern: SSE for server-to-client streaming
const eventSource = new EventSource('/api/stream')

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data)
  // Handle real-time update
}

// Cleanup
useEffect(() => {
  return () => eventSource.close()
}, [])
```

### 2. **File Upload Flow**

```typescript
// Step 1: Create file input
<input
  type="file"
  accept=".csv,.json,.txt,.pdf"
  onChange={handleFileSelect}
  ref={fileInputRef}
/>

// Step 2: Handle file selection
const handleFileSelect = async (e: ChangeEvent<HTMLInputElement>) => {
  const file = e.target.files?.[0]
  if (!file) return
  
  // Validate file
  if (file.size > MAX_FILE_SIZE) {
    showError('File too large')
    return
  }
  
  // Upload file
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  })
}
```

### 3. **State Persistence**

```typescript
// Pattern: Persist state to localStorage
const usePersistedState = <T>(key: string, defaultValue: T) => {
  const [state, setState] = useState<T>(() => {
    const stored = localStorage.getItem(key)
    return stored ? JSON.parse(stored) : defaultValue
  })
  
  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(state))
  }, [key, state])
  
  return [state, setState] as const
}

// Usage
const [theme, setTheme] = usePersistedState('theme', 'dark')
```

## 🔍 Debugging Workflows

### 1. **Component Debugging**

```typescript
// Add debug boundaries
if (process.env.NODE_ENV === 'development') {
  console.log('[Component] Rendering with props:', props)
}

// Track re-renders
useEffect(() => {
  console.log('[Component] Mounted/Updated')
  return () => console.log('[Component] Unmounted')
})

// Debug state changes
const [state, setState] = useState(initial)
const setStateDebug = (newState) => {
  console.log('[State Change]', { from: state, to: newState })
  setState(newState)
}
```

### 2. **Network Debugging**

```typescript
// Intercept fetch for debugging
const debugFetch = async (...args) => {
  console.log('[Fetch] Request:', ...args)
  const response = await fetch(...args)
  console.log('[Fetch] Response:', response.status)
  return response
}

// Use in development
const apiFetch = process.env.NODE_ENV === 'development' ? debugFetch : fetch
```

## 📦 Deployment Workflows

### 1. **Pre-deployment Checklist**

```bash
# 1. Run all checks
pnpm lint && pnpm type-check && pnpm test

# 2. Build locally
pnpm build

# 3. Test production build
pnpm start

# 4. Check environment variables
# Ensure all NEXT_PUBLIC_* vars are set

# 5. Commit and push
git add .
git commit -m "chore: prepare for deployment"
git push origin main
```

### 2. **Vercel Deployment**

```bash
# Automatic deployment on push to main
# Or manual deployment:
vercel --prod

# Preview deployment
vercel

# Check deployment
vercel ls
```

## 🛠️ Maintenance Tasks

### 1. **Dependency Updates**

```bash
# Check outdated packages
pnpm outdated

# Update dependencies safely
pnpm update --interactive

# Test after updates
pnpm test

# Update lock file
pnpm install
```

### 2. **Code Quality Improvements**

```bash
# Find unused code
pnpm depcheck

# Analyze bundle
pnpm build && pnpm analyze

# Check for security issues
pnpm audit
```

## 🚨 Emergency Procedures

### 1. **Production Hotfix**

```bash
# 1. Create hotfix branch from main
git checkout main && git pull
git checkout -b hotfix/issue-description

# 2. Make minimal fix
# Only fix the critical issue

# 3. Test thoroughly
pnpm test

# 4. Deploy immediately
git push origin hotfix/issue-description
# Create PR and merge ASAP
```

### 2. **Rollback Procedure**

```bash
# Vercel rollback
vercel rollback

# Or redeploy previous commit
git checkout <previous-commit>
vercel --prod
```

## 📊 Monitoring & Analytics

### 1. **Performance Monitoring**

```typescript
// Add performance marks
performance.mark('feature-start')
// ... feature code ...
performance.mark('feature-end')
performance.measure('feature', 'feature-start', 'feature-end')

// Log to analytics
const measure = performance.getEntriesByName('feature')[0]
analytics.track('Performance', {
  feature: 'feature-name',
  duration: measure.duration
})
```

### 2. **Error Tracking**

```typescript
// Global error boundary
export function ErrorBoundary({ children }) {
  return (
    <ReactErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('Error caught:', error)
        // Send to error tracking service
        errorTracker.captureException(error, { errorInfo })
      }}
    >
      {children}
    </ReactErrorBoundary>
  )
}
```

---

> "Efficient workflows are the foundation of productive development. Master these patterns for maximum impact."