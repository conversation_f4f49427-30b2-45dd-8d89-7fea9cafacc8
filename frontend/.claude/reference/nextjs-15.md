# Next.js 15 Reference

> Quick reference for Next.js 15 App Router features and patterns used in this project.

## 🔑 Key Features We Use

### App Router
```typescript
// File-based routing
app/
  page.tsx          // Route: /
  layout.tsx        // Shared layout
  (auth)/
    login/
      page.tsx      // Route: /login
  api/
    test/
      route.ts      // API: /api/test
```

### Client Components
```typescript
"use client"  // Required for:
// - useState, useEffect, etc.
// - Event handlers
// - Browser APIs
// - Third-party client libraries
```

### Server Components (Default)
```typescript
// No "use client" directive
// Can:
// - Fetch data directly
// - Access backend resources
// - Keep sensitive data server-side
// - Reduce client bundle size
```

### Metadata
```typescript
export const metadata: Metadata = {
  title: 'Trading Co-pilot',
  description: 'AI-powered trading assistant'
}
```

### Route Handlers
```typescript
// app/api/*/route.ts
export async function GET(request: Request) {
  return Response.json({ data })
}

export async function POST(request: Request) {
  const body = await request.json()
  return new Response('Created', { status: 201 })
}
```

### Streaming Responses
```typescript
// For SSE/streaming
return new Response(stream, {
  headers: {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
  },
})
```

### Image Optimization
```typescript
import Image from 'next/image'

<Image
  src="/image.png"
  alt="Description"
  width={500}
  height={300}
  priority  // For above-fold images
/>
```

### Font Optimization
```typescript
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

<body className={inter.className}>
```

## 🎨 Styling Patterns

### CSS Modules
```typescript
import styles from './component.module.css'
<div className={styles.container}>
```

### Tailwind CSS (Our Choice)
```typescript
<div className="flex items-center justify-center">
```

## 🚀 Performance Features

### Prefetching
```typescript
import Link from 'next/link'
<Link href="/about" prefetch={true}>  // Default true
```

### Loading UI
```typescript
// app/loading.tsx
export default function Loading() {
  return <Skeleton />
}
```

### Error Boundaries
```typescript
// app/error.tsx
'use client'
export default function Error({
  error,
  reset,
}: {
  error: Error
  reset: () => void
}) {
  return <ErrorUI error={error} reset={reset} />
}
```

### Parallel Routes
```typescript
// app/@modal/page.tsx
// app/@sidebar/page.tsx
// Can render multiple pages in parallel
```

## 🔧 Configuration

### next.config.mjs
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['example.com'],
    unoptimized: true  // For static export
  },
  typescript: {
    ignoreBuildErrors: true  // Use carefully
  },
  eslint: {
    ignoreDuringBuilds: true  // Use carefully
  }
}
```

### Middleware
```typescript
// middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Auth checks, redirects, etc.
  return NextResponse.next()
}

export const config = {
  matcher: ['/dashboard/:path*']
}
```

## 📦 Common Patterns

### Data Fetching
```typescript
// Server Component
async function Page() {
  const data = await fetch('https://api.example.com/data', {
    next: { revalidate: 3600 }  // Cache for 1 hour
  })
  
  return <Component data={data} />
}
```

### Route Groups
```typescript
// (marketing)/  - Doesn't affect URL
// (shop)/       - Organize without changing routes
```

### Dynamic Routes
```typescript
// [id]/page.tsx
export default function Page({ params }: { params: { id: string } }) {
  return <div>ID: {params.id}</div>
}
```

### API Routes Best Practices
```typescript
// Type-safe API routes
import { NextRequest } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    // Validate body
    return Response.json({ success: true })
  } catch (error) {
    return Response.json(
      { error: 'Invalid request' },
      { status: 400 }
    )
  }
}
```

## 🚨 Common Gotchas

1. **Client Components**: Can't import server-only modules
2. **Hydration Errors**: Ensure server/client HTML matches
3. **use client**: Applies to entire component tree below
4. **Headers**: Must use headers() function in Server Components
5. **Cookies**: Must use cookies() function in Server Components

## 📚 Useful Links

- [Next.js 15 Docs](https://nextjs.org/docs)
- [App Router Migration](https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration)
- [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers)
- [Data Fetching](https://nextjs.org/docs/app/building-your-application/data-fetching)