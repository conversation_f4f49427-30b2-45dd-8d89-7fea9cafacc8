# AG-UI Protocol Reference

> Complete reference for the Agentic UI (AG-UI) protocol implementation in this project.

## 🔄 Protocol Overview

AG-UI is an event-driven protocol for real-time AI agent communication using Server-Sent Events (SSE).

### Core Concepts
- **Event-Driven**: All communication via typed events
- **Streaming First**: Real-time updates as they happen
- **Stateful**: Maintains conversation context
- **Extensible**: Custom events for specific features

## 📋 Event Types

### Lifecycle Events
```typescript
// Run lifecycle
RUN_STARTED     // Agent begins processing
RUN_FINISHED    // Processing complete
RUN_ERROR       // Error occurred

// Step tracking
STEP_STARTED    // Individual step begins
STEP_FINISHED   // Step completes
```

### Message Events
```typescript
// Text streaming
TEXT_MESSAGE_START    // Message begins
TEXT_MESSAGE_CONTENT  // Content chunks
TEXT_MESSAGE_END      // Message complete

// Example flow:
{ event_type: 'text_message_start', message_id: 'msg_1' }
{ event_type: 'text_message_content', delta: 'Hello' }
{ event_type: 'text_message_content', delta: ' world!' }
{ event_type: 'text_message_end', message_id: 'msg_1' }
```

### Custom Events (Trading Co-pilot)
```typescript
// Thinking process
THINKING_UPDATE       // Agent reasoning steps

// Code execution
CODE_BLOCK_START     // Code block begins
CODE_BLOCK_CONTENT   // Code content
CODE_EXECUTION_RESULT // Execution output

// Artifacts
ARTIFACT_START       // Artifact streaming begins
ARTIFACT_CONTENT     // Content chunks
ARTIFACT_END         // Streaming complete
ARTIFACT_UPDATE      // Metadata updates

// Interaction
REFINEMENT_REQUEST   // Agent needs clarification
MEMORY_OPERATION     // State persistence
```

## 🔌 Client Implementation

### Hook Usage
```typescript
import { useAGUIChat } from '@/hooks/useAGUIChat'

function Component() {
  const {
    messages,           // Message history
    isStreaming,        // Currently streaming?
    error,             // Error state
    currentMessage,     // Message being streamed
    currentStep,        // Current processing step
    streamingArtifacts, // Active artifacts
    sendMessage,        // Send new message
    stopStreaming,      // Abort stream
    clearMessages       // Clear history
  } = useAGUIChat({
    apiUrl: 'http://localhost:8002',
    agentId: 'TradingCoPilotRoot',
    userId: 'user_123',
    sessionId: 'session_456'
  })
  
  // Send message
  const handleSend = async () => {
    await sendMessage('Analyze my portfolio')
  }
}
```

### Event Processing
```typescript
const handleAGUIEvent = (event: AGUIEventUnion) => {
  switch (event.event_type) {
    case AGUIEventType.TEXT_MESSAGE_CONTENT:
      // Append to current message
      setCurrentMessage(prev => ({
        ...prev,
        content: prev.content + event.delta
      }))
      break
      
    case AGUIEventType.THINKING_UPDATE:
      // Add thinking step
      currentThinkingRef.current.push(event.content)
      break
      
    case AGUIEventType.ARTIFACT_START:
      // Initialize artifact
      streamingArtifacts.set(event.artifact_id, {
        id: event.artifact_id,
        type: event.type,
        title: event.title,
        content: '',
        status: 'streaming'
      })
      break
  }
}
```

## 🎨 Message Structure

### User Message
```typescript
interface UserMessage {
  id: string
  role: 'user'
  content: string
  timestamp: number
  fileInfo?: {
    filename: string
    size: number
    type: string
  }
}
```

### Assistant Message
```typescript
interface AssistantMessage {
  id: string
  role: 'assistant'
  content: string           // Main response
  thinking?: string[]       // Reasoning steps
  codeBlocks?: CodeBlock[]  // Executable code
  artifacts?: Artifact[]    // Generated artifacts
  timestamp: number
}
```

## 🔧 Server Implementation

### SSE Endpoint
```typescript
export async function GET() {
  const encoder = new TextEncoder()
  const stream = new ReadableStream({
    async start(controller) {
      const sendEvent = (event: any) => {
        const data = `data: ${JSON.stringify(event)}\n\n`
        controller.enqueue(encoder.encode(data))
      }
      
      // Send events
      sendEvent({
        event_type: 'run_started',
        event_id: 'evt_1',
        timestamp: Date.now(),
        sequence: 1
      })
      
      // ... more events
      
      controller.close()
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
```

### Request Format
```typescript
interface RunAgentRequest {
  agent_id: string
  user_id: string
  session_id: string
  input: {
    messages: Array<{
      role: 'user' | 'assistant'
      content: string
    }>
    file_info?: FileInfo  // Optional file attachment
  }
  stream: boolean
}
```

## 📦 Artifact Types

### Supported Artifacts
```typescript
type ArtifactType = 
  | 'json'      // Structured data
  | 'code'      // Executable code
  | 'metrics'   // Performance metrics
  | 'chart'     // Chart configuration
  | 'table'     // Tabular data
  | 'report'    // Markdown report
  | 'analysis'  // Analysis document
```

### Artifact Structure
```typescript
interface Artifact {
  id: string
  type: ArtifactType
  title: string
  description?: string
  content?: string      // Text content
  data?: any           // Structured data
  content_type?: 'text' | 'json' | 'markdown'
  status?: 'streaming' | 'complete'
}
```

## 🎯 Best Practices

### 1. Event Ordering
```typescript
// Always check sequence numbers
if (event.sequence !== expectedSequence + 1) {
  console.warn('Out of order event:', event)
}
```

### 2. Error Handling
```typescript
// Handle connection errors
onerror: (error) => {
  if (error.name !== 'AbortError') {
    setError('Connection lost. Retrying...')
    // Implement retry logic
  }
}
```

### 3. Memory Management
```typescript
// Use refs for streaming data
const streamingDataRef = useRef(new Map())

// Clean up on unmount
useEffect(() => {
  return () => {
    abortControllerRef.current?.abort()
  }
}, [])
```

### 4. State Synchronization
```typescript
// Finalize messages properly
const finalizeMessage = () => {
  const finalMessage = {
    ...currentMessage,
    thinking: currentThinkingRef.current,
    codeBlocks: Array.from(codeBlocksRef.current.values()),
    artifacts: artifactsRef.current
  }
  
  setMessages(prev => [...prev, finalMessage])
  resetStreamingState()
}
```

## 🔍 Debugging

### Enable Debug Logging
```typescript
const DEBUG = process.env.NODE_ENV === 'development'

if (DEBUG) {
  console.log('[AG-UI Event]', {
    type: event.event_type,
    sequence: event.sequence,
    data: event
  })
}
```

### Common Issues

1. **Events out of order**: Check network latency
2. **Duplicate messages**: Ensure unique message IDs
3. **Memory leaks**: Clean up event listeners
4. **Connection drops**: Implement reconnection logic

## 📚 Resources

- [AG-UI SDK Documentation](/ag-ui/docs/)
- [SSE Specification](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [Event-Driven Architecture](https://en.wikipedia.org/wiki/Event-driven_architecture)