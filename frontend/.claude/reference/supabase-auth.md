# Supabase Authentication Reference

> Quick reference for Supabase Auth implementation in this project.

## 🔐 Setup & Configuration

### Environment Variables
```bash
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### Client Creation
```typescript
// lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

### Server Client
```typescript
// lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export function createClient() {
  const cookieStore = cookies()
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )
}
```

## 🎭 Auth Context Pattern

### Provider Implementation
```typescript
const AuthContext = createContext<AuthContextType>()

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return (
    <AuthContext.Provider value={{ user, loading, ...methods }}>
      {children}
    </AuthContext.Provider>
  )
}
```

## 🔑 Authentication Methods

### Email/Password Sign In
```typescript
const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  if (error) throw error
  return data
}
```

### Sign Up
```typescript
const signUp = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`,
    },
  })
  
  if (error) throw error
  return data
}
```

### OAuth Providers
```typescript
const signInWithOAuth = async (provider: 'github' | 'google') => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
    },
  })
  
  if (error) throw error
  return data
}
```

### Sign Out
```typescript
const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}
```

### Password Reset
```typescript
const resetPassword = async (email: string) => {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/callback?type=recovery`,
  })
  
  if (error) throw error
  return data
}
```

## 🛡️ Route Protection

### Middleware Implementation
```typescript
// middleware.ts
import { updateSession } from '@/lib/supabase/middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

### Protected Pages
```typescript
// app/dashboard/page.tsx
export default function DashboardPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) return <LoadingSpinner />
  if (!user) return null

  return <Dashboard user={user} />
}
```

## 📊 User Management

### Get User Profile
```typescript
const getProfile = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('Not authenticated')
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
    
  if (error) throw error
  return data
}
```

### Update User Metadata
```typescript
const updateProfile = async (updates: ProfileUpdate) => {
  const { data, error } = await supabase.auth.updateUser({
    data: updates
  })
  
  if (error) throw error
  return data
}
```

## 🔄 Session Management

### Get Current Session
```typescript
const getSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error) throw error
  return session
}
```

### Refresh Session
```typescript
const refreshSession = async () => {
  const { data: { session }, error } = await supabase.auth.refreshSession()
  
  if (error) throw error
  return session
}
```

### Session Persistence
```typescript
// Sessions are automatically persisted in cookies
// Configure in createServerClient options:
{
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
}
```

## 🎯 Common Patterns

### Auth Loading State
```typescript
function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth()
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner />
      </div>
    )
  }
  
  if (!user) {
    return <Navigate to="/login" />
  }
  
  return <>{children}</>
}
```

### Role-Based Access
```typescript
const checkRole = async (requiredRole: string) => {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return false
  
  const { data } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', user.id)
    .single()
    
  return data?.role === requiredRole
}
```

### Auth Callback Handler
```typescript
// app/auth/callback/route.ts
export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const supabase = createClient()
    await supabase.auth.exchangeCodeForSession(code)
  }

  return NextResponse.redirect(requestUrl.origin)
}
```

## 🚨 Error Handling

### Common Auth Errors
```typescript
try {
  await signIn(email, password)
} catch (error) {
  if (error.message.includes('Invalid login credentials')) {
    // Handle invalid credentials
  } else if (error.message.includes('Email not confirmed')) {
    // Handle unverified email
  } else {
    // Handle other errors
  }
}
```

### Error Messages
```typescript
const AUTH_ERRORS = {
  'Invalid login credentials': 'Incorrect email or password',
  'Email not confirmed': 'Please verify your email',
  'User already registered': 'An account already exists',
  'Password should be at least 6 characters': 'Password too short'
}

const getErrorMessage = (error: Error) => {
  return AUTH_ERRORS[error.message] || 'An error occurred'
}
```

## 🔒 Security Best Practices

1. **Never expose service role key** in client code
2. **Use Row Level Security (RLS)** for data access
3. **Validate redirects** to prevent open redirects
4. **Implement rate limiting** for auth endpoints
5. **Use secure password requirements**
6. **Enable MFA** for sensitive applications

## 📚 Resources

- [Supabase Auth Docs](https://supabase.com/docs/guides/auth)
- [Next.js Auth Guide](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)
- [RLS Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [OAuth Setup](https://supabase.com/docs/guides/auth/social-login)