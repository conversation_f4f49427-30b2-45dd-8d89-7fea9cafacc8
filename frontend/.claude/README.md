# 🤖 .claude Configuration

> This directory contains persistent configuration, documentation, and tools that enhance <PERSON>'s capabilities across all conversations.

## 📁 Directory Structure

```
.claude/
├── README.md           # This file
├── CLAUDE.md          # Core capabilities & commitments
├── PROJECT.md         # Project-specific documentation
├── STANDARDS.md       # Coding standards & patterns
├── WORKFLOWS.md       # Common workflows & tasks
├── scripts/           # Automation scripts
│   ├── analyze-bundle.sh      # Bundle size analysis
│   ├── check-types.sh         # TypeScript validation
│   ├── generate-component.sh  # Component scaffolding
│   └── health-check.sh        # Project health check
├── reference/         # Technology reference docs
├── technologies/      # Learning & discoveries
└── context/          # Important project context
```

## 🚀 Quick Start

### Run Health Check
```bash
./.claude/scripts/health-check.sh
```

### Generate New Component
```bash
./.claude/scripts/generate-component.sh MyComponent
```

### Check TypeScript Types
```bash
./.claude/scripts/check-types.sh
```

### Analyze Bundle Size
```bash
./.claude/scripts/analyze-bundle.sh
```

## 📖 Key Documents

### [CLAUDE.md](./CLAUDE.md)
My core capabilities, commitments, and how to get maximum value from our collaboration.

### [PROJECT.md](./PROJECT.md)
Complete project overview including architecture, tech stack, and feature roadmap.

### [STANDARDS.md](./STANDARDS.md)
Coding standards, patterns, and best practices for maintaining code quality.

### [WORKFLOWS.md](./WORKFLOWS.md)
Common development workflows, debugging techniques, and deployment procedures.

## 🎯 Purpose

This configuration enables me to:

1. **Remember Context**: All documentation persists across conversations
2. **Maintain Standards**: Consistent code quality and patterns
3. **Automate Tasks**: Scripts for common operations
4. **Learn & Improve**: Document discoveries and solutions
5. **Maximize Efficiency**: Leverage parallel operations and background research

## 💡 Usage Tips

### For Maximum Effectiveness

1. **Reference these docs** when asking questions
   ```
   "Following the patterns in STANDARDS.md, implement..."
   ```

2. **Use the scripts** for common tasks
   ```
   "Run the health check script and fix any issues"
   ```

3. **Update documentation** as the project evolves
   ```
   "Update PROJECT.md with the new feature we just added"
   ```

4. **Leverage my capabilities**
   - Ask me to read multiple files in parallel
   - Have me research documentation in the background
   - Request batch operations across the codebase

### Adding New Documentation

Place new docs in appropriate directories:
- `/reference/` - External API/library documentation
- `/technologies/` - Learning about new tech/patterns
- `/context/` - Project-specific context and decisions

## 🔧 Maintenance

### Keep Documentation Current
- Update when architecture changes
- Document new patterns discovered
- Add scripts for repetitive tasks

### Regular Reviews
- Run health check weekly
- Update dependencies monthly
- Review and refactor quarterly

## 🤝 Collaboration

This setup ensures:
- **Consistency**: Same standards across all interactions
- **Efficiency**: Automated workflows and scripts
- **Quality**: Enforced patterns and best practices
- **Learning**: Continuous improvement and documentation

---

> "This configuration transforms me from a coding assistant into your dedicated technical partner, learning and growing with your project."