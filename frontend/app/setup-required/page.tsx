import Link from 'next/link'
import { AlertCircle, ExternalLink } from 'lucide-react'

export default function SetupRequiredPage() {
  return (
    <div className="min-h-screen bg-[#131314] text-white flex items-center justify-center p-6">
      <div className="max-w-2xl w-full">
        <div className="bg-[#1a1a1b] rounded-xl border border-[#3a3a3a]/50 p-8">
          <div className="flex items-center gap-3 mb-6">
            <AlertCircle className="w-8 h-8 text-yellow-500" />
            <h1 className="text-2xl font-semibold">Supabase Setup Required</h1>
          </div>
          
          <div className="space-y-4 text-[#c5c5d2]">
            <p>
              The Trading Copilot requires Supabase authentication to be configured. 
              Please follow these steps to set up your environment:
            </p>
            
            <div className="bg-[#1c1c1d] rounded-lg p-6 space-y-4">
              <h2 className="text-white font-medium text-lg mb-3">Quick Setup Steps:</h2>
              
              <ol className="space-y-3 list-decimal list-inside">
                <li>
                  <span className="text-[#8e8ea0]">Create a free account at </span>
                  <a 
                    href="https://supabase.com" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 inline-flex items-center gap-1"
                  >
                    supabase.com
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </li>
                
                <li>
                  <span className="text-[#8e8ea0]">Create a new project and wait for it to initialize</span>
                </li>
                
                <li>
                  <span className="text-[#8e8ea0]">Go to Settings → API to find your credentials</span>
                </li>
                
                <li>
                  <span className="text-[#8e8ea0]">Copy </span>
                  <code className="bg-[#2a2a2b] px-2 py-1 rounded text-sm">.env.local.example</code>
                  <span className="text-[#8e8ea0]"> to </span>
                  <code className="bg-[#2a2a2b] px-2 py-1 rounded text-sm">.env.local</code>
                </li>
                
                <li>
                  <span className="text-[#8e8ea0]">Add your Supabase URL and anon key to </span>
                  <code className="bg-[#2a2a2b] px-2 py-1 rounded text-sm">.env.local</code>
                </li>
                
                <li>
                  <span className="text-[#8e8ea0]">Restart the development server</span>
                </li>
              </ol>
            </div>
            
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <p className="text-sm">
                <strong className="text-blue-400">Detailed Guide:</strong> Check out{' '}
                <code className="bg-[#2a2a2b] px-2 py-1 rounded text-xs">SUPABASE_SETUP.md</code>{' '}
                in the project root for comprehensive setup instructions including database tables and auth configuration.
              </p>
            </div>
            
            <div className="pt-4">
              <h3 className="font-medium text-white mb-2">Required Environment Variables:</h3>
              <pre className="bg-[#1c1c1d] rounded-lg p-4 text-sm overflow-x-auto">
{`NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here`}
              </pre>
            </div>
          </div>
          
          <div className="mt-8 flex gap-4">
            <a
              href="https://app.supabase.com"
              target="_blank"
              rel="noopener noreferrer"
              className="px-6 py-2.5 bg-blue-500 hover:bg-blue-600 rounded-lg font-medium transition-colors inline-flex items-center gap-2"
            >
              Open Supabase
              <ExternalLink className="w-4 h-4" />
            </a>
            
            <Link
              href="/"
              className="px-6 py-2.5 bg-[#2a2a2b] hover:bg-[#3a3a3a] rounded-lg font-medium transition-colors"
            >
              Try Again
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}