"use client"

import React, { useState } from 'react'
import { AgentCanvas } from '@/components/agent-canvas'
import { useAgentUI } from '@/hooks/useAgentUI'
import { Button } from '@/components/ui/button'
import { AgentScenarios } from '@/lib/agent-ui-protocol'

// Demo page showing agent-driven UI capabilities
export default function AgentDemoPage() {
  const { components, processCommand, processBatch, rearrangeLayout, isProcessing } = useAgentUI()
  const [scenario, setScenario] = useState<string>('')

  // Simulate different agent scenarios
  const runScenario = async (scenarioName: string) => {
    setScenario(scenarioName)
    
    switch (scenarioName) {
      case 'chart_analysis':
        // Simulate agent analyzing a chart
        const chartCommands = AgentScenarios.chartAnalysis(
          {
            type: 'line',
            data: generateMockChartData(),
            title: 'BTC/USD Analysis',
          },
          {
            insights: [
              'Strong uptrend detected with 15% gain over 7 days',
              'Volume surge indicates institutional interest',
              'Key resistance at $48,000 approaching'
            ],
            patterns: [
              { type: 'Ascending Triangle', confidence: 0.85 },
              { type: 'Volume Breakout', confidence: 0.92 }
            ],
            recommendations: [
              'Consider taking partial profits near $48,000',
              'Set stop-loss at $45,500 to protect gains'
            ]
          }
        )
        
        await processBatch(chartCommands.map((cmd, i) => ({
          id: `chart_cmd_${i}`,
          timestamp: new Date().toISOString(),
          ...cmd
        })))
        break

      case 'market_monitor':
        // Real-time market monitoring
        const monitorCommands = AgentScenarios.marketMonitoring(['BTC', 'ETH', 'SOL', 'AVAX'])
        await processBatch(monitorCommands.map((cmd, i) => ({
          id: `monitor_cmd_${i}`,
          timestamp: new Date().toISOString(),
          ...cmd
        })))
        break

      case 'trade_setup':
        // Interactive trade builder
        const tradeCommands = AgentScenarios.tradingSetup({
          symbol: 'BTC/USD',
          entry: 47500,
          stop: 46800,
          targets: [48200, 48800, 49500]
        })
        await processBatch(tradeCommands.map((cmd, i) => ({
          id: `trade_cmd_${i}`,
          timestamp: new Date().toISOString(),
          ...cmd
        })))
        break

      case 'adaptive_layout':
        // Demonstrate layout adaptation
        rearrangeLayout('grid')
        setTimeout(() => rearrangeLayout('stack'), 2000)
        setTimeout(() => rearrangeLayout('focus'), 4000)
        break
    }
  }

  // Handle component interactions
  const handleComponentInteraction = (componentId: string, interaction: any) => {
    console.log('Component interaction:', componentId, interaction)
    
    // Agent could respond to interactions
    if (interaction.type === 'query') {
      processCommand({
        id: `response_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: 'create',
        payload: {
          componentType: 'insight_popup',
          props: {
            content: `Analyzing ${interaction.query}...`,
            position: interaction.position
          },
          layout: {
            position: 'absolute',
            coordinates: { x: interaction.x, y: interaction.y },
            size: { width: 300, height: 200 },
            // Animation will be handled by CSS transitions
          }
        }
      })
    }
  }

  return (
    <div className="flex flex-col h-screen bg-[#131314]">
      {/* Control Panel */}
      <div className="p-4 border-b border-gray-800 bg-gray-900/50 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-white">Agent-Driven UI Demo</h1>
          <div className="flex gap-2">
            <Button
              onClick={() => runScenario('chart_analysis')}
              disabled={isProcessing}
              variant={scenario === 'chart_analysis' ? 'default' : 'outline'}
              size="sm"
            >
              Chart Analysis
            </Button>
            <Button
              onClick={() => runScenario('market_monitor')}
              disabled={isProcessing}
              variant={scenario === 'market_monitor' ? 'default' : 'outline'}
              size="sm"
            >
              Market Monitor
            </Button>
            <Button
              onClick={() => runScenario('trade_setup')}
              disabled={isProcessing}
              variant={scenario === 'trade_setup' ? 'default' : 'outline'}
              size="sm"
            >
              Trade Setup
            </Button>
            <Button
              onClick={() => runScenario('adaptive_layout')}
              disabled={isProcessing}
              variant={scenario === 'adaptive_layout' ? 'default' : 'outline'}
              size="sm"
            >
              Adaptive Layout
            </Button>
          </div>
        </div>
        {isProcessing && (
          <div className="mt-2 text-sm text-blue-400">
            Agent is manipulating the UI...
          </div>
        )}
      </div>

      {/* Agent Canvas */}
      <div className="flex-1 relative">
        <AgentCanvas 
          components={components}
          onComponentInteraction={handleComponentInteraction}
        />
      </div>

      {/* Status Bar */}
      <div className="p-2 border-t border-gray-800 bg-gray-900/50 backdrop-blur-sm">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <span>Components: {components.length}</span>
          <span>Scenario: {scenario || 'None'}</span>
          <span>Status: {isProcessing ? 'Processing' : 'Ready'}</span>
        </div>
      </div>
    </div>
  )
}

// Generate mock chart data
function generateMockChartData() {
  const now = Date.now()
  const data = []
  let price = 45000

  for (let i = 0; i < 50; i++) {
    price += (Math.random() - 0.5) * 500
    data.push({
      time: now - (50 - i) * 3600000,
      price: price,
      volume: Math.random() * 1000000
    })
  }

  return data
}