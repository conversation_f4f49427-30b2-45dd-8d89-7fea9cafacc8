"use client"
import { TradingCopilot } from "@/components/trading-copilot"
import { useAuth } from "@/contexts/auth-context"
import { usePageStability } from "@/hooks/usePageStability"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function TradingPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  
  // Prevent aggressive page refreshes
  usePageStability()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/landing")
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return <TradingCopilot />
}