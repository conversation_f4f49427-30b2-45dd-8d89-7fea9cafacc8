import { NextResponse } from 'next/server'

export async function POST() {
  const encoder = new TextEncoder()
  const stream = new ReadableStream({
    async start(controller) {
      const sendEvent = (event: any) => {
        const data = `data: ${JSON.stringify(event)}\n\n`
        controller.enqueue(encoder.encode(data))
      }

      // Simulate SSE events with artifacts
      
      // Send acknowledgment
      sendEvent({
        type: 'ack'
      })

      await sleep(100)

      // Start thinking
      sendEvent({
        type: 'thinking_start'
      })

      await sleep(100)

      sendEvent({
        type: 'thinking_content',
        content: 'Analyzing trading data and preparing comprehensive report...'
      })

      await sleep(500)

      sendEvent({
        type: 'thinking_content',
        content: '\nCalculating key metrics and performance indicators...'
      })

      await sleep(500)

      // End thinking
      sendEvent({
        type: 'thinking_end'
      })

      await sleep(100)

      // Start streaming message with full analysis in chat
      const messageChunks = [
        "I've completed the analysis of your trading data. ",
        "Here are the key findings:\n\n",
        "## Trading Performance Summary\n\n",
        "Your trading performance over the analyzed period shows mixed results:\n\n",
        "### Key Metrics:\n",
        "- **Total Trades**: 35\n",
        "- **Win Rate**: 54.29% (above average)\n", 
        "- **Profit Factor**: 1.25 (positive edge)\n",
        "- **Maximum Drawdown**: -68.49% (concerning)\n",
        "- **Sharpe Ratio**: 0.81 (moderate risk-adjusted returns)\n\n",
        "### Analysis Insights:\n\n",
        "1. **Profitability**: While you maintain a positive win rate and profit factor, ",
        "the total net profit of $57.51 suggests room for improvement in position sizing.\n\n",
        "2. **Risk Management**: The significant maximum drawdown of 68.49% indicates ",
        "periods of substantial equity decline that could be mitigated.\n\n",
        "3. **Consistency**: Your average win ($14.13) exceeds your average loss ($11.02), ",
        "which is a positive sign for long-term profitability.\n\n",
        "### Recommendations:\n\n",
        "1. **Implement stricter stop-loss rules** to reduce maximum drawdown\n",
        "2. **Focus on trade quality** over quantity\n",
        "3. **Consider position sizing** based on volatility\n",
        "4. **Review your risk-reward ratios** for each trade setup\n\n",
        "I've also generated the raw metrics data and analysis code in the artifacts panel for your reference.\n\n"
      ]

      for (const chunk of messageChunks) {
        sendEvent({
          type: 'text',
          content: chunk
        })
        await sleep(50)
      }

      // Start artifact 1: JSON metrics
      sendEvent({
        type: 'artifact_start',
        metadata: {
          id: 'artifact_metrics',
          type: 'json',
          title: 'Trading Metrics',
          description: 'Key performance indicators from your trading analysis'
        }
      })

      await sleep(200)

      // Stream JSON content
      const jsonContent = JSON.stringify({
        "total_trades": 35,
        "total_profit": 57.51,
        "win_rate": "54.29%",
        "profit_factor": 1.25,
        "max_drawdown": "-68.49%",
        "sharpe_ratio": 0.81,
        "average_win": 14.13,
        "average_loss": -11.02
      }, null, 2)

      sendEvent({
        type: 'artifact_content',
        content: jsonContent,
        metadata: {
          id: 'artifact_metrics'
        }
      })

      await sleep(100)

      sendEvent({
        type: 'artifact_end',
        metadata: {
          id: 'artifact_metrics'
        }
      })

      await sleep(300)

      // Start artifact 2: Python code
      sendEvent({
        type: 'artifact_start',
        metadata: {
          id: 'artifact_code',
          type: 'code',
          title: 'Trading Analysis Code',
          description: 'Python code for analyzing trading performance'
        }
      })

      await sleep(200)

      const codeContent = `import pandas as pd
import numpy as np

def analyze_trading_performance(df):
    """Analyze trading performance metrics"""
    
    # Calculate basic metrics
    total_trades = len(df)
    winning_trades = df[df['profit'] > 0]
    losing_trades = df[df['profit'] < 0]
    
    win_rate = len(winning_trades) / total_trades * 100
    avg_win = winning_trades['profit'].mean()
    avg_loss = losing_trades['profit'].mean()
    
    # Calculate profit factor
    gross_profit = winning_trades['profit'].sum()
    gross_loss = abs(losing_trades['profit'].sum())
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else np.inf
    
    return {
        'total_trades': total_trades,
        'win_rate': f"{win_rate:.2f}%",
        'average_win': avg_win,
        'average_loss': avg_loss,
        'profit_factor': profit_factor
    }`

      sendEvent({
        type: 'artifact_content',
        content: codeContent,
        metadata: {
          id: 'artifact_code'
        }
      })

      await sleep(100)

      sendEvent({
        type: 'artifact_end',
        metadata: {
          id: 'artifact_code'
        }
      })

      // Finish message
      sendEvent({
        type: 'text',
        content: "Would you like me to dive deeper into any specific aspect of your trading performance?"
      })

      await sleep(100)

      // Send completion
      sendEvent({
        type: 'complete'
      })

      controller.close()
    }
  })

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}