import { NextRequest, NextResponse } from 'next/server'
import { getBackendUrl, API_ENDPOINTS } from '@/lib/config/api'

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json()
    
    // Log what we received
    console.log('Test backend received:', body)
    
    // Get backend URL from centralized config
    const backendUrl = getBackendUrl()
    
    // Forward the request to the actual backend
    const response = await fetch(`${backendUrl}${API_ENDPOINTS.CHAT_STREAM}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    })
    
    // Log the response
    console.log('Backend response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    })
    
    // If not ok, return the error
    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json({
        error: 'Backend request failed',
        status: response.status,
        statusText: response.statusText
        // Removed backend URL and error details for security
      }, { status: response.status })
    }
    
    // Return success with the response
    return new NextResponse(response.body, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      }
    })
    
  } catch (error: any) {
    console.error('Test backend error:', error)
    return NextResponse.json({
      error: 'Failed to connect to backend',
      message: 'Service temporarily unavailable'
      // Removed detailed error info and backend URL for security
    }, { status: 500 })
  }
}

export async function GET() {
  const backendUrl = getBackendUrl()
  
  try {
    const response = await fetch(backendUrl)
    return NextResponse.json({
      reachable: response.ok,
      status: response.status,
      statusText: response.statusText
      // Removed backend URL for security
    })
  } catch (error: any) {
    return NextResponse.json({
      reachable: false,
      error: 'Service check failed'
      // Removed backend URL and error details for security
    })
  }
}