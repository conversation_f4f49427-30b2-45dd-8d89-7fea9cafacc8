import { NextRequest, NextResponse } from 'next/server'
import { waitlistRateLimiter, getClientIp } from '@/lib/rate-limit'

// In-memory storage for demo purposes
// In production, you'd use a proper database
let waitlistEntries: Array<{
  id: number
  email: string
  position: number
  createdAt: Date
}> = []

let nextId = 1

export async function POST(request: NextRequest) {
  try {
    // Check rate limit
    const clientIp = getClientIp(request)
    const rateLimitResult = waitlistRateLimiter.check(clientIp)
    
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': '5',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
            'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
          }
        }
      )
    }

    const body = await request.json()
    const { email } = body

    // Basic validation
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: 'Valid email required' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingEntry = waitlistEntries.find(entry => entry.email === email)
    if (existingEntry) {
      return NextResponse.json(
        { 
          message: 'Already registered',
          position: existingEntry.position 
        },
        { status: 200 }
      )
    }

    // Create new waitlist entry
    const position = waitlistEntries.length + 1
    const newEntry = {
      id: nextId++,
      email,
      position,
      createdAt: new Date()
    }

    waitlistEntries.push(newEntry)

    // In production, you might want to:
    // 1. Save to database
    // 2. Send welcome email
    // 3. Add to email marketing list
    // 4. Trigger analytics event

    console.log(`New waitlist signup: ${email} (Position: ${position})`)

    return NextResponse.json({
      message: 'Successfully joined waitlist',
      position: position
    }, {
      headers: {
        'X-RateLimit-Limit': '5',
        'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
      }
    })

  } catch (error) {
    console.error('Waitlist signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  // Optional: Get waitlist stats
  return NextResponse.json({
    total: waitlistEntries.length,
    message: 'Waitlist stats'
  })
}