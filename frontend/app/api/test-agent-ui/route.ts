import { NextResponse } from 'next/server'

export async function POST() {
  const encoder = new TextEncoder()
  const stream = new ReadableStream({
    async start(controller) {
      const sendEvent = (event: any) => {
        const data = `data: ${JSON.stringify(event)}\n\n`
        controller.enqueue(encoder.encode(data))
      }

      // Send acknowledgment
      sendEvent({
        type: 'ack'
      })

      await sleep(100)

      // Send a text message first
      sendEvent({
        type: 'text',
        content: "I'm analyzing your chart and creating an interactive visualization for you..."
      })

      await sleep(500)

      // Send agent UI command to create an intelligent chart
      sendEvent({
        type: 'agent_ui_command',
        command: {
          id: `cmd_${Date.now()}`,
          type: 'create',
          payload: {
            componentId: 'chart_analysis_1',
            componentType: 'intelligent_chart',
            props: {
              symbol: 'NDQ',
              title: 'NASDAQ-100 Index Analysis',
              timeframe: '1-hour',
              data: {
                labels: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
                datasets: [{
                  label: 'Price',
                  data: [22500, 22550, 22600, 22580, 22620, 22640, 22680, 22642],
                  borderColor: '#3b82f6',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)'
                }]
              },
              analysis: {
                insights: [
                  'Strong uptrend detected with higher highs and higher lows',
                  'Price holding above key support at 22,600',
                  'RSI showing overbought conditions at 72',
                  'Volume increasing on upward moves'
                ],
                patterns: [
                  { type: 'Ascending Triangle', description: 'Bullish continuation pattern' },
                  { type: 'Support Zone', description: '22,400-22,500 area' }
                ],
                recommendations: [
                  'Consider waiting for pullback to 22,600 support',
                  'Set stop loss below 22,400',
                  'Target resistance at 22,800'
                ],
                keyMetrics: {
                  trend: 'Bullish',
                  strength: 8,
                  risk: 'Medium'
                }
              }
            },
            layout: {
              position: { x: 10, y: 10, width: 80, height: 80 },
              zIndex: 1
            }
          }
        }
      })

      await sleep(1000)

      // Send more text
      sendEvent({
        type: 'text',
        content: "\n\nBased on the chart analysis, here are the key points:\n\n**Technical Analysis Summary:**\n- Clear uptrend pattern with ascending triangle formation\n- Strong support established at 22,600 level\n- RSI indicating overbought conditions - caution advised\n- Volume profile confirms bullish momentum\n\n**Trading Strategy:**\n1. Wait for pullback to support levels for better entry\n2. Implement trailing stop loss to protect profits\n3. Monitor for potential breakout above 22,700\n"
      })

      await sleep(500)

      // Create another component - insight cards
      sendEvent({
        type: 'agent_ui_command',
        command: {
          id: `cmd_${Date.now()}`,
          type: 'create',
          payload: {
            componentId: 'insights_1',
            componentType: 'intelligent_chart',
            props: {
              title: 'Market Sentiment Indicators',
              data: {
                labels: ['Fear', 'Neutral', 'Greed'],
                datasets: [{
                  label: 'Sentiment',
                  data: [15, 25, 60],
                  backgroundColor: ['#ef4444', '#6b7280', '#10b981']
                }]
              },
              analysis: {
                insights: [
                  'Market sentiment leaning bullish (60% greed)',
                  'Institutional positioning net long',
                  'Options flow showing call buying'
                ]
              }
            },
            layout: {
              position: { x: 10, y: 50, width: 35, height: 40 },
              zIndex: 2
            }
          }
        }
      })

      await sleep(500)

      // Send completion
      sendEvent({
        type: 'complete'
      })

      controller.close()
    }
  })

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}