"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowRight, CheckCircle, Brain, Target, BarChart3, Zap, Sparkles, TrendingUp } from "lucide-react"
import { cn } from "@/lib/utils"

const features = [
  {
    title: "Analyze current market sentiment",
    subtitle: "Real-time insights across crypto, stocks, and forex",
    icon: Brain,
    color: "blue",
  },
  {
    title: "Review portfolio performance",
    subtitle: "Risk assessment and optimization opportunities",
    icon: Target,
    color: "violet",
  },
  {
    title: "Technical analysis on BTC/ETH",
    subtitle: "Chart patterns and key technical indicators",
    icon: BarChart3,
    color: "green",
  },
  {
    title: "Optimize trading strategy",
    subtitle: "AI-powered recommendations for your approach",
    icon: Zap,
    color: "orange",
  },
]

export default function WaitlistPage() {
  const [email, setEmail] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [position, setPosition] = useState<number | null>(null)
  const [currentFeature, setCurrentFeature] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || isSubmitting) return

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/waitlist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()
      if (response.ok) {
        setPosition(data.position)
        setIsSubmitted(true)
      }
    } catch (error) {
      console.error('Error joining waitlist:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const CurrentIcon = features[currentFeature].icon

  return (
    <div className="min-h-screen bg-[#0a0a0b] text-white relative overflow-hidden">
      {/* Background Layer */}
      <div className="absolute inset-0 z-0">
        <div className="h-full w-full relative">
          {/* Gradient Backgrounds */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-float" />
            <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-violet-500/5 rounded-full blur-3xl animate-pulse" />
          </div>

          {/* Grid Pattern */}
          <div 
            className="absolute inset-0 opacity-20" 
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }} 
          />

          {/* Dashboard Cards */}
          <div className="absolute top-20 left-10 w-80 h-48 bg-[#131314]/80 backdrop-blur-xl rounded-2xl border border-[#2a2a2a]/30 p-6 transform rotate-[-4deg] hover:rotate-[-2deg] transition-transform duration-500 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-[#8e8ea0]">Portfolio Value</span>
              <TrendingUp className="h-4 w-4 text-green-400" />
            </div>
            <div className="text-3xl font-bold mb-2">$124,580.42</div>
            <div className="text-sm text-green-400">+12.5% (24h)</div>
            <div className="mt-4 h-16 bg-gradient-to-r from-transparent via-green-400/20 to-transparent rounded" />
          </div>

          <div className="absolute top-40 right-10 w-72 h-44 bg-[#131314]/80 backdrop-blur-xl rounded-2xl border border-[#2a2a2a]/30 p-6 transform rotate-[3deg] hover:rotate-[1deg] transition-transform duration-500 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-[#8e8ea0]">BTC Analysis</span>
              <BarChart3 className="h-4 w-4 text-blue-400" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-[#8e8ea0]">Support</span>
                <span className="text-white">$42,100</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-[#8e8ea0]">Resistance</span>
                <span className="text-white">$44,800</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-[#8e8ea0]">RSI</span>
                <span className="text-orange-400">68.4</span>
              </div>
            </div>
          </div>

          <div className="absolute bottom-20 left-20 w-64 h-40 bg-[#131314]/80 backdrop-blur-xl rounded-2xl border border-[#2a2a2a]/30 p-6 transform rotate-[-2deg] hover:rotate-[0deg] transition-transform duration-500 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-[#8e8ea0]">AI Signals</span>
              <Sparkles className="h-4 w-4 text-violet-400" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse" />
                <span className="text-sm">ETH: Strong Buy</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-yellow-400 animate-pulse" />
                <span className="text-sm">SOL: Hold</span>
              </div>
            </div>
          </div>

          <div className="absolute bottom-40 right-20 w-72 h-36 bg-[#131314]/80 backdrop-blur-xl rounded-2xl border border-[#2a2a2a]/30 p-6 transform rotate-[2deg] hover:rotate-[0deg] transition-transform duration-500 shadow-2xl">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-[#8e8ea0]">Recent Trades</span>
              <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse" />
            </div>
            <div className="space-y-1.5 text-xs">
              <div className="flex justify-between">
                <span className="text-green-400">BUY BTC</span>
                <span>0.245 @ $43,280</span>
              </div>
              <div className="flex justify-between">
                <span className="text-red-400">SELL ETH</span>
                <span>2.8 @ $2,340</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className={cn(
          "max-w-2xl w-full transition-all duration-700",
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
        )}>
          {!isSubmitted ? (
            <div>
              {/* Hero */}
              <div className="text-center mb-12">
                <div className="relative inline-block mb-8">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-violet-600 rounded-full blur-2xl opacity-50 animate-pulse" />
                  <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-violet-600 flex items-center justify-center font-bold text-2xl shadow-2xl">
                    TC
                  </div>
                </div>

                <h1 className="text-6xl md:text-7xl font-normal tracking-tight mb-6 leading-tight">
                  Experience the
                  <br />
                  <span className="relative">
                    <span className="font-medium bg-gradient-to-r from-blue-400 via-violet-400 to-blue-400 bg-clip-text text-transparent bg-[length:200%_auto] animate-gradient">
                      Future of Trading
                    </span>
                    <div className="absolute -bottom-2 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50" />
                  </span>
                </h1>

                <p className="text-xl text-[#c5c5d2] mb-2 max-w-xl mx-auto leading-relaxed">
                  Join thousands of traders getting exclusive early access to our AI-powered trading copilot
                </p>
                
                <div className="h-8 mb-8">
                  <div className="text-sm text-blue-400 font-medium flex items-center justify-center gap-2">
                    <CurrentIcon className="h-4 w-4" />
                    <span className="animate-fade-in" key={currentFeature}>
                      {features[currentFeature].title}
                    </span>
                  </div>
                </div>
              </div>

              {/* Form */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-violet-500/10 rounded-3xl blur-xl" />
                <div className="relative bg-[#131314]/60 backdrop-blur-2xl rounded-3xl border border-[#2a2a2a]/50 p-8 shadow-2xl">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-medium mb-2">Get Early Access</h3>
                      <p className="text-sm text-[#8e8ea0]">Be among the first to trade smarter with AI</p>
                    </div>

                    <div className="relative">
                      <Input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder=" "
                        required
                        className="peer w-full h-16 bg-[#1e1e1f]/80 backdrop-blur border-[#2a2a2a] text-white pl-4 pr-40 text-lg rounded-2xl focus:border-blue-400/50 transition-all duration-300 focus:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                      />
                      <label className="absolute left-4 top-1/2 -translate-y-1/2 text-[#8e8ea0] transition-all duration-300 peer-focus:top-0 peer-focus:text-xs peer-focus:bg-[#131314] peer-focus:px-2 peer-focus:text-blue-400 peer-[&:not(:placeholder-shown)]:top-0 peer-[&:not(:placeholder-shown)]:text-xs peer-[&:not(:placeholder-shown)]:bg-[#131314] peer-[&:not(:placeholder-shown)]:px-2">
                        Enter your email
                      </label>
                      <Button
                        type="submit"
                        disabled={isSubmitting || !email}
                        className="absolute right-2 top-1/2 -translate-y-1/2 h-12 px-8 bg-gradient-to-r from-blue-500 to-violet-500 hover:from-blue-600 hover:to-violet-600 text-white font-medium rounded-xl transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl hover:scale-105 active:scale-100"
                      >
                        {isSubmitting ? (
                          <span className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                            Joining...
                          </span>
                        ) : (
                          <span className="flex items-center gap-2">
                            Join Waitlist
                            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                          </span>
                        )}
                      </Button>
                    </div>

                    <div className="flex items-center justify-center gap-8 text-sm text-[#8e8ea0]">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span>No credit card required</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span>Priority access</span>
                      </div>
                    </div>
                  </form>
                </div>
              </div>

              {/* Features */}
              <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
                {features.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <div
                      key={index}
                      className="group relative overflow-hidden rounded-xl bg-[#131314]/40 backdrop-blur border border-[#2a2a2a]/30 p-4 hover:border-[#2a2a2a]/50 transition-all duration-300"
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-[#1e1e1f]/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <Icon className={cn(
                        "h-5 w-5 mb-2",
                        feature.color === "blue" && "text-blue-400",
                        feature.color === "violet" && "text-violet-400",
                        feature.color === "green" && "text-green-400",
                        feature.color === "orange" && "text-orange-400"
                      )} />
                      <p className="text-xs font-medium leading-tight">{feature.title}</p>
                    </div>
                  )
                })}
              </div>
            </div>
          ) : (
            <div className="text-center animate-in fade-in slide-in-from-bottom-4 duration-700">
              <div className="relative inline-block mb-8">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full blur-2xl opacity-50 animate-pulse" />
                <div className="relative w-24 h-24 rounded-full bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-2xl">
                  <CheckCircle className="h-12 w-12 text-white" />
                </div>
              </div>
              
              <h2 className="text-5xl font-medium mb-4 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                You&apos;re on the list!
              </h2>
              
              <p className="text-xl text-[#c5c5d2] mb-2">
                You&apos;re #{position} in line for early access
              </p>
              
              <p className="text-sm text-[#8e8ea0] mb-8">
                Check your email for confirmation and updates
              </p>
              
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-3xl blur-xl" />
                <div className="relative bg-[#131314]/60 backdrop-blur-2xl rounded-3xl border border-[#2a2a2a]/50 p-8 max-w-md mx-auto shadow-2xl">
                  <h3 className="font-medium text-lg mb-4">What happens next?</h3>
                  <ul className="space-y-3 text-left">
                    <li className="flex items-start gap-3">
                      <div className="w-5 h-5 rounded-full bg-green-400/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <div className="w-2 h-2 rounded-full bg-green-400" />
                      </div>
                      <span className="text-sm text-[#c5c5d2]">We&apos;ll send you an exclusive invitation when your spot is ready</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-5 h-5 rounded-full bg-green-400/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <div className="w-2 h-2 rounded-full bg-green-400" />
                      </div>
                      <span className="text-sm text-[#c5c5d2]">Get early access to all Trading Co-pilot features</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-5 h-5 rounded-full bg-green-400/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <div className="w-2 h-2 rounded-full bg-green-400" />
                      </div>
                      <span className="text-sm text-[#c5c5d2]">Join our community of early traders and shape the product</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0) translateX(0); }
          25% { transform: translateY(-20px) translateX(10px); }
          50% { transform: translateY(0) translateX(-10px); }
          75% { transform: translateY(20px) translateX(5px); }
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
        
        .animate-gradient {
          animation: gradient 4s ease infinite;
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
      `}</style>
    </div>
  )
}