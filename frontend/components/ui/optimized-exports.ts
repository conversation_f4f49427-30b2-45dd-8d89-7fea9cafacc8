/**
 * Optimized UI component exports
 * Only export what's actually used to reduce bundle size
 */

// Core components that are always needed
export { Button } from './button'
export { Input } from './input'
export { Alert, AlertDescription, AlertTitle } from './alert'

// Lazy load heavy components
import { lazy } from 'react'

export const Dialog = lazy(() => import('./dialog').then(mod => ({ 
  default: mod.Dialog 
})))
export const DialogContent = lazy(() => import('./dialog').then(mod => ({ 
  default: mod.DialogContent 
})))
export const DialogHeader = lazy(() => import('./dialog').then(mod => ({ 
  default: mod.DialogHeader 
})))
export const DialogTitle = lazy(() => import('./dialog').then(mod => ({ 
  default: mod.DialogTitle 
})))

export const DropdownMenu = lazy(() => import('./dropdown-menu').then(mod => ({ 
  default: mod.DropdownMenu 
})))
export const DropdownMenuContent = lazy(() => import('./dropdown-menu').then(mod => ({ 
  default: mod.DropdownMenuContent 
})))
export const DropdownMenuItem = lazy(() => import('./dropdown-menu').then(mod => ({ 
  default: mod.DropdownMenuItem 
})))
export const DropdownMenuTrigger = lazy(() => import('./dropdown-menu').then(mod => ({ 
  default: mod.DropdownMenuTrigger 
})))

export const Sheet = lazy(() => import('./sheet').then(mod => ({ 
  default: mod.Sheet 
})))
export const SheetContent = lazy(() => import('./sheet').then(mod => ({ 
  default: mod.SheetContent 
})))
export const SheetHeader = lazy(() => import('./sheet').then(mod => ({ 
  default: mod.SheetHeader 
})))
export const SheetTitle = lazy(() => import('./sheet').then(mod => ({ 
  default: mod.SheetTitle 
})))

// Export other commonly used components normally
export { Badge } from './badge'
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card'
export { Checkbox } from './checkbox'
export { Label } from './label'
export { Progress } from './progress'
export { RadioGroup, RadioGroupItem } from './radio-group'
export { ScrollArea } from './scroll-area'
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select'
export { Separator } from './separator'
export { Skeleton } from './skeleton'
export { Slider } from './slider'
export { Switch } from './switch'
export { Tabs, TabsContent, TabsList, TabsTrigger } from './tabs'
export { Textarea } from './textarea'
export { Toggle } from './toggle'
export { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip'