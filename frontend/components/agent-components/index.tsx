// Agent Components Registry
// These components are designed to be dynamically created by the AI agent

export { IntelligentChart } from '@/components/unified-intelligent-chart'

// Component type mapping for the agent UI system
export const AGENT_COMPONENT_MAP = {
  intelligent_chart: () => import('@/components/unified-intelligent-chart').then(m => m.IntelligentChart),
  // Add more components as they are created:
  // metrics_panel: () => import('./metrics-panel').then(m => m.MetricsPanel),
  // order_flow: () => import('./order-flow').then(m => m.OrderFlow),
  // market_grid: () => import('./market-grid').then(m => m.MarketGrid),
  // insight_cards: () => import('./insight-cards').then(m => m.InsightCards),
}

// Helper to get component by type
export async function getAgentComponent(type: string) {
  const loader = AGENT_COMPONENT_MAP[type as keyof typeof AGENT_COMPONENT_MAP]
  if (loader) {
    return await loader()
  }
  return null
}